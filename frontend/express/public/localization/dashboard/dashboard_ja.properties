#common
common.total-sessions = Total Sessions
common.total-sessions-description = Number of times your application is opened, by new or returning users, in the selected time period.
common.new-sessions = New Sessions
common.new-sessions-description = Number of times your application is opened by a new user, in the selected time period. It is equal to the number of New Users and it only counts the first session the user had.
common.unique-sessions = Unique Sessions
common.unique-sessions-description = Number of times your application is opened by a new or returning user from a unique device, in the selected time period. It is equal to the number of Total Users.
common.total-users = åè¨ã¦ã¼ã¶ã¼
common.new-users = æ°ã¦ã¼ã¶ã¼
common.returning-users = ãªã¿ã¼ã³ã¦ã¼ã¶ã¼
common.date = æ¥æ
common.month = Month
common.percent = ãã¼ã»ã³ã
common.number-of-users = ã¦ã¼ã¶ã¼æ°
common.number-of-sessions = ã»ãã·ã§ã³æ°
common.session-duration = Session Duration
common.table.no-data = ãã¼ã¿ãããã¾ãã
common.search.no-match-found = No match found
common.table.total-users = åè¨ã¦ã¼ã¶ã¼
common.table.total-users-desc = The number of users (unique devices/IDs) who have opened your application in the selected time period.
web.common.table.total-users = Total Visitors
web.common.table.total-users-desc = The number of visitors (unique devices/IDs) who have visited your website in the selected time period.
common.table.new-users = æ°ã¦ã¼ã¶ã¼
common.table.new-users-desc = The number of first-time users (unique devices/IDs) in the selected time period.
common.table.returning-users = ãªã¿ã¼ã³ã¦ã¼ã¶ã¼
common.table.returning-users-desc = Number of users using your application for the second or later time, in the selected time period, calculated as Total Users (less) New Users.
web.common.table.returning-users = Returning Visitors
web.common.table.returning-users-desc = Number of visitors using your website for the second or later time, in the selected time period, calculated as Total Visitors (less) New Visitors.
common.table.total-sessions = åè¨ã»ãã·ã§ã³
common.table.new-sessions = æ°è¦ã»ãã·ã§ã³
common.table.unique-sessions = ã¦ãã¼ã¯ã»ãã·ã§ã³
common.today = ä»æ¥
common.7days = 7æ¥é
common.30days = 30æ¥é
common.60days = 60æ¥é
common.90days = 90æ¥é
common.from = éå§æ¥
common.to  = çµäºæ¥
common.download = Download
common.refresh = Refresh
common.compare-to = æ¬¡ã¨æ¯è¼
common.no-comparison = æ¯è¼ãªã
common.of-total = of Total
common.comparison-desc = åãã°ã©ãã®éå»ã®ãã¼ã¿ãè¡¨ç¤ºããã«ã¯æ¯è¼ã¿ã¤ããé¸æãã¦ãã ãã
common.yesterday = æ¨æ¥
common.previous-period = ååã®æé
common.previous-year = åå¹´ã®åææ
common.bar.top-resolution = Top Resolutions
common.bar.top-resolution.description = Top 5 resolution settings of the devices used your users' sessions, in the selected time period.
web.common.bar.top-resolution.description = Top 5 resolution settings of the devices used your visitors' sessions, in the selected time period.
common.bar.top-carrier = Top Carriers
common.bar.top-users = ã¦ã¼ã¶ã¼ä¸ä½
common.bar.top-platform = Top Platforms
common.bar.top-platform.description = Top 5 versions of the platforms of your usersâ sessions, in the selected time period.
web.common.bar.top-platform.description = Top 5 versions of the platforms of your visitorsâ sessions, in the selected time period.
common.bar.top-platform-version = Top Platform Versions
common.bar.top-platform-version.description = Top 3 versions of the platforms of your users' sessions, in the selected time period.
web.common.bar.top-platform-version.description = Top 3 versions of the platforms of your visitors' sessions, in the selected time period.
common.bar.top-devices = Top Devices
common.bar.top-devices.description = Top 5 devices of your usersâ based on their sessions, in the selected time period.
web.common.bar.top-devices.description = Top 5 devices of your visitorsâ based on their sessions, in the selected time period.
common.bar.top-device-types = Top Device types
common.bar.top-device-types.description = Top 5 device types of your usersâ based on their sessions, in the selected time period.
web.common.bar.top-device-types.description = Top 5 device types of your visitorsâ based on their sessions, in the selected time period.
common.bar.top-browsers = Top Browsers
common.bar.top-browsers.description = Top 5 browsers of your usersâ based on their sessions, in the selected time period
common.bar.top-app-versions = Top App Versions
common.bar.top-app-versions.description = Top 5 App versions of your usersâ based on their sessions, in the selected time period.
web.common.bar.top-app-versions.description = Top 5 App versions of your visitorsâ based on their sessions, in the selected time period.
common.bar.no-data = ãã¼ã¿ãããã¾ãã
common.apply = é©ç¨ãã
common.apply-changes = Apply changes
common.cancel = ã­ã£ã³ã»ã«
common.select = Select
common.select-all = å¨ã¦é¸æ
common.deselect-all = å¨ã¦é¸æè§£é¤
common.all = ãã¹ã¦
common.select-type = ã¿ã¤ããé¸æ
common.yes = ã¯ã
common.no = ããã
common.done = å®äº
common.save = ä¿å­ãã
common.saving = ä¿å­
common.close = éãã
common.copy-id = ã¦ã£ã¸ã§ããIDãã³ãã¼
common.loading = èª­ã¿è¾¼ã¿ä¸­
common.continue = ç¶ãã
common.okay = OK
common.add = è¿½å ãã
common.delete = åé¤ãã
common.edit = ç·¨éãã
common.lock = ã­ãã¯
common.locked = ã­ãã¯æ¸ã¿
common.unlock = ã­ãã¯è§£é¤
common.unlocked = ã­ãã¯è§£é¤æ¸ã¿
common.ok = OK
common.total = åè¨
common.status = ã¹ãã¼ã¿ã¹
common.select-status = ã¹ãã¼ã¿ã¹ãé¸æ
common.select-type = ã¿ã¤ããé¸æ
common.type = ã¿ã¤ã
common.time = æé
common.started = éå§æé
common.info = æå ±
common.save-changes = å¤æ´ãä¿å­
common.graph-max = {2} ã«ãããæå¤§ <b>{0}</b> {1}
common.graph-min = {2} ã«ãããæå° <b>{0}</b> {1}
common.graph.time-spent = å©ç¨æé(å)
common.graph.average-time = å¹³åå©ç¨æé(å)
common.graph.reqs-received = ãªã¯ã¨ã¹ãåä¿¡
common.graph.avg-reqs-received = å¹³åãªã¯ã¨ã¹ãåä¿¡
common.graph.no-data = æå®ãããæéåã®ãã¼ã¿ãããã¾ãã
common.seconds = ç§
common.minutes = å
common.hour = æé
common.second.abrv = ç§
common.second.abrv2 = ç§
common.minute.abrv = å
common.minute.abrv2 = å
common.hour.abrv = æé
common.hour.abrv2 = æé
common.day.abrv = æ¥
common.day.abrv2 = æ¥
common.year.abrv = å¹´
common.year.abrv2 = å¹´
common.buckets.monthly = æé
common.buckets.weekly = é±é
common.buckets.daily = æ¥é
common.buckets.years = years
common.buckets.months = æ
common.buckets.weeks = é±
common.buckets.days = æ¥
common.max = æå¤§
common.create = ä½æ
common.session-expiration = ã»ãã·ã§ã³çµäº
common.click-to-login = ã­ã°ã¤ã³ãä¿æãã
common.expire-minute = ã»ãã·ã§ã³çµäºã¾ã§1å
common.expire-seconds = ã»ãã·ã§ã³çµäºã¾ã§10ç§
common.add-new-app = æ°ããã¢ããªè¿½å 
common.add-new-app-button = æ°è¦ã¢ããªãè¿½å 
common.change-app-type = ã¢ããªã¿ã¤ãå¤æ´
common.install-plugin = ãã©ã°ã¤ã³ãã¤ã³ã¹ãã¼ã«ãã¦ãã ããã
common.or = ã¾ãã¯
common.missing-type = ããã·ã¥ãã¼ãã¿ã¤ããç°ãªã
common.feedback-and-support = Feedback & Support
common.provide-feedback = Provide Feedback
common.support = ãµãã¼ã
common.documentation = ãã­ã¥ã¡ã³ãã¼ã·ã§ã³
common.help-center = Help Center
common.feature-request = Request a Feature
common.showing = _TOTAL_ä»¶ä¸­ã®_START_ ï½_END_ä»¶ãè¡¨ç¤º
common.filtered = ï¼åè¨_MAX_ä»¶ãããã£ã«ã¿ã¼ï¼
common.enterprise-edition = Countly Enterprise (supported)
common.save-to-csv = CSVã§ä¿å­
common.save-to-excel = Excelã§ä¿å­
common.search = æ¤ç´¢
common.unknown = ä¸æ
common.eu = ã¨ã¼ã­ãã
common.integrate-sdks = SDKã¤ã³ãã°ã¬ã¼ã·ã§ã³ã«é¢ãããµãã¼ããå¿è¦ã§ããï¼
common.integrate-sdks-text = helps you generate personalized code snippets and SDK integration tutorials based on your platforms and Countly features you want to use.
common.integrate-sdks-platforms = ãã©ãããã©ã¼ã ãé¸æãã¦ãã ãã
common.go-to-countries = Go to Countries
common.show = è¡¨ç¤º
common.show-items = è¡¨ç¤º
common.items-per-page = Items per page
common.try-later = å¦çãå®è¡ã§ãã¾ãããååº¦ãè©¦ããã ãã
common.never = ãªã
common.in.days = {0}æ¥å¾
common.in.hours = {0}æéå¾
common.in.minutes = {0}åå¾
common.in.seconds = {0}ç§å¾
common.ago.just-now = ãã£ãä»
common.ago.seconds-ago = {0}ç§å
common.ago.half-minute = 30ç§å
common.ago.less-minute = 1åä»¥å
common.ago.one-minute = 1åå
common.ago.minutes-ago = {0}åå
common.ago.one-hour = 1æéå
common.ago.hours-ago = {0}æéå
common.ago.one-day = 1æ¥å
common.ago.days-ago = {0}æ¥å
common.ago.one-week = 1é±éå
common.every.minutes = {0}åãã¨
common.every.hours = {0}æéãã¨
common.every.hour = æ¯æé
common.estimation = ãã®æéã®åè¨ (åºæ) å¤ã¯ãæå¹ãªæ¥æ¬¡ãé±éãæéã¹ãã¼ã¿ã¹ããæå¤§ã®ã¿ã¤ã ãã±ãããä½¿ç¨ãã¦æ¨å®ããä¿®æ­£ããã¾ãã <br/><br/>æ­£ç¢ºãªåè¨ã«ã¦ã³ãã¯ä»å¹´ãæãæ¥æ°ã®æéã«å¯¾ãã¦å©ç¨å¯è½ã§ãã
common.action = ã¢ã¯ã·ã§ã³
common.view = ãã¥ã¼
common.back = æ»ã
common.success = æå
common.error = ã¨ã©ã¼
common.no-clear = æ¶å»ããªã
common.yes-discard = åãæ¶ã
common.yes-clear-it = æ¶å»ãã
common.no-dont-change = å¤æ´ããªã
common.no-dont-delete = åé¤ããªã
common.no-dont-continue = ç¶è¡ããªã
common.no-dont-do-that  = å®è¡ããªã
common.last-updated = æçµæ´æ°
common.location = ã­ã±ã¼ã·ã§ã³
common.enable = æå¹ã«ãã
common.running = å®è¡ä¸­
common.completed = å®äº
common.stopped = Stopped
common.errored = ã¨ã©ã¼
common.visits = äººã®è¨ªåè
common.select-columns-to-display = è¡¨ç¤ºããåãé¸æ
common.enter-value = å¤ãå¥å
common.optional = ä»»æ
common.clear-values = å¤ãæ¶å»
common.confirm = Are you sure?
common.json-editor = JSONã¨ãã£ã¿ã¼
common.valid-json = æå¹ãªJSONã³ã¼ã
common.invalid-json = ç¡å¹ãªJSONã³ã¼ã
common.format = ãã©ã¼ããã
common.saveJSON = JSONãä¿å­
common.zoom-in = Zoom
common.cancel-zoom = Cancel zoom
common.zoom-out = Zoom out
common.zoom-reset = Reset Zoom
common.zoom-info = Select an area in the chart to zoom in
common.group = GROUP
common.send = Send
common.customize = Customize
common.breakdown-not-available = Breakdown is not available
common.breakdown-not-available-desc = Breakdown is not available for given time period. There is no data.
common.internal-events = Internal Events
common.custom-events = Custom Events
common.none = None
common.emtpy-view-title = ...hmm, seems empty here
common.emtpy-view-subtitle = No data found
common.no-widget-text = Add a widget to create dashboard and see <br/> some informative explanation
common.no-dashboard-text = Create dashboard and see<br/> some informative explanation
common.create-dashboard = + Create a Dashboard
common.add-widget = + Add a Widget
common.sunday = Sunday
common.monday = Monday
common.tuesday = Tuesday
common.wednesday = Wednesday
common.thursday = Thursday
common.friday = Friday
common.saturday = Saturday
common.hours = Hours
common.chart-type = Chart Type
common.adjust-limit = Adjust Limit
common.copy-error-message = Something went wrong, please copy it again.
common.created-at-by = Created <span>{0}</span> by {1}
common.updated = Updated

#vue
common.undo = Undo
common.drawer.next-step = Next step
common.drawer.previous-step = Previous step
common.diff-helper.changes = You made {0} changes.
common.diff-helper.keep = Do you want to keep them?
common.save-changes = å¤æ´ãä¿å­
common.discard-changes = Discard
common.complete = Complete
common.reset = Reset
common.confirm = Are you sure?
common.remove = Remove
common.next = Next
common.previous = Previous
common.select-at-least = At least {0} items should be selected.
common.select-at-most = At most {0} items can be selected.
common.and = and
common.or = ã¾ãã¯
common.enter-email-addresses=Enter Email Addresses
common.invalid-email-address="{0}" is not a valid email address
common.email-example=(e.g. <EMAIL>)
common.no-email-addresses=No addresses have been specified
#period-picker
common.all-time = All time
common.in-last-days-plural = in the last {0} days
common.in-last-days = in the last day
common.in-last-weeks-plural = in the last {0} weeks
common.in-last-weeks = in the last week
common.in-last-months-plural = in the last {0} months
common.in-last-months = in the last month
common.time-period-select.custom-range = Custom Range
common.time-period-select.last-n = In the Last
common.time-period-select.since = Since
common.time-period-select.on = On
common.time-period-select.range = In Between
common.time-period-name.since = since {0}
common.time-period-name.on = on {0}
common.time-period-select.before = Before
common.time-period-name.before = Before {0}
common.time-period-name.range = {0} - {1}
common.apply-range = Apply Range
common.range-length-limit = Selected range cannot be longer than {0}.

#taskmanager
taskmanager.rerun = åå®è¡
taskmanager.stop = Stop
taskmanager.view-old = ã­ã£ãã·ã¥ãè¡¨ç¤º
taskmanager.confirm-delete-title = ã¬ãã¼ããåé¤ãã¾ããï¼
taskmanager.confirm-stop = Are you sure you want to stop this task?
taskmanager.confirm-delete = {0}ã®ã¬ãã¼ããåé¤ãã¾ããï¼
taskmanager.confirm-rerun = ãã®ã¿ã¹ã¯ãåå®è¡ãã¾ããï¼
taskmanager.confirm-rerun-title = ã¿ã¹ã¯ãåå®è¡ãã¾ããï¼
taskmanager.confirm-stop-title = Stop task?
taskmanager.yes-rerun-task = ãã®ã¿ã¹ã¯ãåå®è¡ãã
taskmanager.yes-stop-task = Yes, stop this task
taskmanager.yes-delete-report = ã¬ãã¼ããåé¤ãã
taskmanager.rerunning = åå®è¡ä¸­
taskmanager.select-origin = ãã¼ã¿åãé¸æ
taskmanager.origin = ãã¼ã¿å
taskmanager.auto = èªåãªãã¬ãã·ã¥
taskmanager.manual = ã¯ã³ã¿ã¤ã 
taskmanager.edit = ç·¨é
taskmanager.last-today = ä»æ¥
taskmanager.last-7days = éå»7æ¥é
taskmanager.last-30days = éå»30æ¥é
taskmanager.last-60days = éå»60æ¥é
taskmanager.manually-table-remind = ããªããããªãã®ãã¼ã ã«ãã£ã¦ä½æãããã¯ã³ã¿ã¤ã ã®èªåãªãã¬ãã·ã¥ã¿ã¤ãã®ã¬ãã¼ãã§ã
taskmanager.automatically-table-remind = ã¯ã¨ãªã¼ãå®äºããã¾ã§ã«é·æéãããå ´åã«èªåçã«çæãããã¬ãã¼ãã§ã
taskmanager.query-added-long = Query has been added to long running queries
taskmanager.empty-warning = There are no long running queries
taskmanager.recalculating = Recalculating




#task manager assistent notification strings
assistant.taskmanager.longTaskTooLong.title = This request is running for too long.
assistant.taskmanager.longTaskTooLong.message = We have switched it to report manager and will notify you when it is finished.
assistant.taskmanager.longTaskTooLong.info = åç¨®æ©è½ -> ã¬ãã¼ãããã¼ã¸ã£ã¼ã§ã¹ãã¼ã¿ã¹ãç¢ºèªãã¦ãã ãã
assistant.taskmanager.longTaskAlreadyRunning.title = A similar report is already running.
assistant.taskmanager.longTaskAlreadyRunning.message = åããã©ã¡ã¼ã¿ã®ããã¬ãã¼ãã<a href='#/manage/tasks'>ã¬ãã¼ãããã¼ã¸ã£ã¼</a>ã§ãã§ã«å®è¡ä¸­ã®ããã§ã
assistant.taskmanager.completed.title = ã¬ãã¼ãã{1}ã«å¯¾ãã¦å®æãã¾ãã
assistant.taskmanager.completed.message = Results are ready for {0} reports. Check report manager to view them.
assistant.taskmanager.errored.title = {0}ã®ã¬ãã¼ãã®çæã«å¤±æãã¾ãã
assistant.taskmanager.errored.message = Some reports ({0}) couldn't be generated. Check report manager to try rerunning it.
assistant.taskmanager.errored.info = åç¨®æ©è½ -> ã¿ã¹ã¯ããã¼ã¸ã£ã¼ã®ä¸

#placeholders
placeholder.old-password = å¤ããã¹ã¯ã¼ã
placeholder.new-password = æ°ãããã¹ã¯ã¼ã
placeholder.again = ããä¸åº¦
placeholder.app-name = ã¢ããªã±ã¼ã·ã§ã³ã®ååãå¥å
placeholder.search-columns = Search Columns

placeholder.events.edit.description = Enter event description
placeholder.events.edit.count = Count
placeholder.events.edit.sum = Sum
placeholder.events.edit.duration = Duration

#application categories
application-category.books = æ¸ç±
application-category.business = ãã¸ãã¹
application-category.education = æè²
application-category.entertainment = ã¨ã³ã¿ã¼ãã¤ã¡ã³ã
application-category.finance = ãã¡ã¤ãã³ã¹
application-category.games = ã²ã¼ã 
application-category.health-fitness = å¥åº·&ãã£ãããã¹
application-category.lifestyle = ã©ã¤ãã¹ã¿ã¤ã«
application-category.medical = å»å­¦
application-category.music = ãã¥ã¼ã¸ãã¯
application-category.navigation = ããã²ã¼ã·ã§ã³
application-category.news = ãã¥ã¼ã¹
application-category.photography = åç
application-category.productivity = çç£æ§
application-category.reference = ãªãã¡ã¬ã³ã¹
application-category.social-networking = ã½ã¼ã·ã£ã«ãããã¯ã¼ã¯
application-category.sports = ã¹ãã¼ã
application-category.travel = æè¡
application-category.utilities = ã¦ã¼ãã£ãªãã£
application-category.weather = å¤©æ°

#sidebar
sidebar.home = Home
sidebar.dashboard = æ¦è¦
sidebar.analytics = åæ
sidebar.analytics.users = User Analytics
web.sidebar.analytics.users = Visitor Analytics
sidebar.analytics.user-loyalty = User Loyalty
sidebar.analytics.session = Session Analytics
sidebar.analytics.sessions = ã»ãã·ã§ã³
sidebar.analytics.countries = å½
sidebar.analytics.devices = ããã¤ã¹
sidebar.analytics.app-versions = ã¢ããªãã¼ã¸ã§ã³
sidebar.analytics.versions = ãã¼ã¸ã§ã³
sidebar.analytics.carriers = éä¿¡ã­ã£ãªã¢
sidebar.analytics.platforms = ãã©ãããã©ã¼ã 
sidebar.analytics.resolutions = è§£ååº¦
sidebar.analytics.technology = Technology
sidebar.analytics.technology-description = Overview details of your app or website traffic by your usersâ technology, such as platform, device, resolution, browsers and app version.
sidebar.analytics.geo = Geo
sidebar.engagement = ã¨ã³ã²ã¼ã¸ã¡ã³ã
sidebar.events = ã¤ãã³ã
sidebar.events.all-events = å¨ã¤ãã³ã
sidebar.events.blueprint = ã¤ãã³ãã®ç®¡ç
sidebar.events.overview = æ¦è¦
sidebar.utilities = åç¨®æ©è½
sidebar.management = ç®¡ç
sidebar.management.applications = ã¢ããªã±ã¼ã·ã§ã³
sidebar.management.account = ãã¤ã¢ã«ã¦ã³ã
sidebar.management.users = User Management
sidebar.management.longtasks = ã¬ãã¼ãããã¼ã¸ã£ã¼
sidebar.management.help = ãã«ãã¢ã¼ã
sidebar.management.jobs = Jobs
sidebar.management.token-manager = Token Manager
sidebar.settings = è¨­å®
sidebar.logout = ã­ã°ã¢ã¦ã
sidebar.api_key = APIã­ã¼
sidebar.behavior = è¡å
sidebar.category.understand = çè§£
sidebar.category.explore = è©³ç´°åæ
sidebar.category.reach = ãªã¼ã
sidebar.category.improve = æ¹å
sidebar.long-running-queries = Long running queries
sidebar.feedback = Feedback
sidebar.dashboard-tooltip = Dashboards
sidebar.main-menu = Main Menu
sidebar.my-profile = My Profile
sidebar.copy-api-key-success-message = Api Key has been copied to clipboard!

#dashboard
dashboard.apply = é©ç¨
dashboard.home-desc = Overview of collected data
dashboard.empty-title = Nothing to show
dashboard.empty-text = There are no data to show. Enable features or Customize this page to see data about choosen features.
dashboard.audience = Audience
dashboard.customize-home = Customize Home
dashboard.avg-time-spent = Avg. Session Duration
dashboard.avg-time-spent-desc = The average amount of time spent per session on your application. It is calculated by dividing total duration spent across sessions by the total number of sessions.
dashboard.time-spent = Time Spent
dashboard.time-spent-desc = Total time spent for this period
dashboard.reqs-received = ãªã¯ã¨ã¹ãåä¿¡
dashboard.avg-reqs-received = Avg. Requests Received
dashboard.avg-reqs-received-desc = Number of write API requests Countly Server receives for each session (includes sessions, session extensions, events, etc)
dashboard.bounce-rate = ãã¦ã³ã¹ç
dashboard.pages-per-visit = 1è¨ªåå½ããã®ãã¼ã¸
dashboard.note-title-remaining = æ®ãã®æå­æ°
dashboard.go-to-sessions = Go to Sessions
dashboard.total-sessions-desc = Total session data
#users
users.title = ã¦ã¼ã¶ã¼

#user-activity
user-activity.title = User Activity
web.user-activity.title = Visitor Activity
user-activity.description = Overview of the total number of users who started a session on your application, distributed in pre-set categories of numbers of sessions.
web.user-activity.description = Overview of the total number of visitors who started a session on your website, distributed in pre-set categories of numbers of sessions.
user-activity.barchart-all-users = All Users
web.user-activity.barchart-all-users = All Visitors
user-activity.barchart-thirty-days = Active Users (30 days)
web.user-activity.barchart-thirty-days = Active Visitors (30 days)
user-activity.barchart-seven-days = Active Users (7 days)
web.user-activity.barchart-seven-days = Active Visitors (7 days)
user-activity.table-session-count = Session Count (All Time)
user-activity.table-all-users = All Users
web.user-activity.table-all-users = All Visitors
user-activity.table-thirty-days = Active Users (30 days)
web.user-activity.table-thirty-days = Active Visitors (30 days)
user-activity.table-seven-days = Active Users (7 days)
web.user-activity.table-seven-days = Active Visitors (7 days)

#user-loyalty
user-loyalty.range.first-session = First Session
user-loyalty.range.hours = æé
user-loyalty.range.day = æ¥
user-loyalty.range.days = æ¥

#session-overview
session-overview.title = Session Overview
session-overview.description = Summary of all sessions your users have had in your application, in the selected time period.

#session-durations
session-durations.title = Session Durations
session-durations.description = Time period(s) for which users have opened your application.

#session-frequency
session-frequency.title = Session Frequency
session-frequency.description = Number of times users open your application, in the selected time period, distributed into frequency ranges.
session-frequency.table.frequency = Time since last session

#notes
notes.add-new-note = Add New Note
notes.edit-note = Edit Note
notes.note-details = Note Details
notes.note = ã¡ã¢
notes.type = Type
notes.owner = Owner
notes.enter-note = Enter your note
notes.visibility = Visibility
notes.date-and-time = Date and Time
notes.color = Color
notes.color-note-description = Select the indicator color of your note
notes.share-with = Share With
notes.shared-with = Shared With
notes.manage-notes = Manage Notes
notes.delete-note = Do you really want to delete note called {0}?
notes.all-notes = All Notes
notes.back-link = Back to Overview
notes.add-note = Add Note
notes.show-notes = Show Notes
notes.hide-notes = Hide Notes
notes.created-message = Note created successfully

#session-duration
session-duration.title = ã»ãã·ã§ã³æé
session-duration.table.duration = ã»ãã·ã§ã³æé

#countries
countries.title = Countries
countries.description = An overview of the geographical distribution of your users and their sessions in the selected time period.
web.countries.description = An overview of the geographical distribution of your visitors and their sessions in the selected time period.
countries.table.country = å½
countries.table.city = é½å¸
countries.back-to-list = å½ã®ãªã¹ãã¸æ»ã
countries.back-to-map = Back to World Map
countries.google-api-key-remind = é½å¸ã¬ãã«ãã¼ã¿ãè¡¨ç¤ºããã«ã¯ãGoogle Maps APIã­ã¼ãè¨­å®ããå¿è¦ãããã¾ãããã¡ããã¯ãªãã¯ãã¦Google Maps APIã­ã¼ãè¨­å®ãã¦ãã ããã 
countries.go-to-countries = Go to Countries

#devices
devices.title = ããã¤ã¹
devices.table.device = ããã¤ã¹
devices.devices-and-types.title = Devices and Types
devices.go-to-technology = Go to Technology

#analytics users
user-analytics.overview-title = Users Overview
user-analytics.overview-desc = Overview of the main metrics and stats about your audience.
web.user-analytics.overview-title = Visitors Overview


#resolutions
resolutions.title = Resolutions
resolutions.description = Detailed information on the resolution settings of the devices through which your users access your application, in the selected time period.
web.resolutions.description = Detailed information on the resolution settings of the devices through which your visitors access your website, in the selected time period.

resolutions.table.resolution = è§£ååº¦
resolutions.table.width = å¹
resolutions.table.height = é«ã


#device_type
device_type.title = Devices and Types
device_type.description =  Details of the device models and types from which your users access your application, in the selected time period.
web.device_type.description =  Details of the device models and types from which your visitors access your website, in the selected time period.
device_type.table.device_type = Device Type
device_type.types = Types
device_type.devices = Devices

#app-versions
app-versions.title = App Versions
app-versions.description = Detailed information on the application versions of your application accessed by your users, in the selected time period.
web.app-versions.description = Detailed information on the website versions of your website accessed by your visitors, in the selected time period.
app-versions.table.app-version = ã¢ããªãã¼ã¸ã§ã³

#carriers
carriers.title = Carriers
carriers.table.carrier = éä¿¡ã­ã£ãªã¢
carriers.description = Detailed information on the network carriers of the devices through which your users access your application, in the selected time period.

#platforms
platforms.title = Platforms
platforms.pie-right = ãã©ãããã©ã¼ã  ãã¼ã¸ã§ã³
platforms.table.platform = ãã©ãããã©ã¼ã 
platforms.table.platform-version = ãã©ãããã©ã¼ã  ãã¼ã¸ã§ã³
platforms.table.platform-version-for = ãã©ãããã©ã¼ã ãã¼ã¸ã§ã³: 
platforms.description  = Details of the platforms on which yours users access your application, in the selected time period.
web.platforms.description  = Details of the platforms on which yours visitors access your website, in the selected time period.
platforms.platforms-for = Platforms for
platforms.version-distribution = Platforms version distribution
platforms.versions = Versions

#events
events.title = Events
events.blueprint-title = ã¤ãã³ãã®ç®¡ç
events.blueprint-general = ä¸è¬
events.blueprint-events-tab-title = EVENTS
events.blueprint-event-group-title = MANAGE EVENT GROUPS
events.blueprint-event-group-new = New Event Group
events.blueprint-event-group-show.all = All Groups
events.blueprint-event-group-show.visible = Visible Groups
events.blueprint-event-group-show.hidden = Hidden Groups
events.blueprint-events-show.all = All Events
events.blueprint-events-show.hidden = Hidden Events
events.blueprint-events-show.visible = Visible Events
events.go-to-events = Go to Events

events.blueprint-events-properties-tooltip = Edited properties of this event will be updated on All Events and other plugins.
events.blueprint-event-groups-include-events-tooltip = Select at least 2 events to create an Event Group. New Event Groups will automatically  sum all the selected  event properties and report them.
events.blueprint-event-groups-properties-tooltip = Edited properties in this Event Group will be updated across All Events and other plugins.

events.blueprint-event-group-included-events = INCLUDED EVENTS
events.blueprint-eventgroups-tab-title = EVENT GROUPS
events.blueprint-edit = Edit
events.blueprint-drawer-title = Edit Event
events.blueprint-drawer-use-description = Use Description
events.general.description = ã«ã¹ã¿ã ã¤ãã³ãã®ä¸¦ã³æ¿ããè¡¨ç¤ºã»éè¡¨ç¤ºã®ç·¨éãåé¤ã¾ãã¯ã«ã¹ã¿ã ã¤ãã³ãã®å¤è¦³ã®å¤æ´ãã§ãã¾ã
events.general.event = ã¤ãã³ã
events.general.event-description = ã¤ãã³ãã®èª¬æ
events.general.action.perform-action = å®è¡
events.general.action.delete = åé¤
events.general.action.show = è¡¨ç¤º
events.general.action.hide = éè¡¨ç¤º
events.general.none-chosen = 1ã¤ä»¥ä¸ã®ã¤ãã³ããé¸æ
events.general.update-not-successful = ã¤ãã³ãã®æ´æ°ã«å¤±æãã¾ããã
events.general.changes-saved = å¤æ´ãä¿å­ãã¾ãã
events.general.cancel = ã­ã£ã³ã»ã«
events.general.confirm = ç¢ºèª
events.general.status = ã¹ãã¼ã¿ã¹
events.general.status.visible = è¡¨ç¤º
events.general.status.hidden = éè¡¨ç¤º
events.general.show.title = è¡¨ç¤º
events.general.show.all = å¨ã¦
events.general.show.hidden = éè¡¨ç¤º
events.general.show.visible = è¡¨ç¤º
events.general.want-delete = è¤æ°ã®ã¤ãã³ã ({0})ãåé¤ãã¾ããããããã§ããï¼
events.general.want-delete-this = {0}ã®ã¤ãã³ããåé¤ãã¾ããããããã§ããï¼
events.general.want-delete-this-title = ã¤ãã³ããåé¤ãã¾ããï¼
events.general.want-delete-title = ã¤ãã³ããåé¤ãã¾ããï¼
events.general.yes-delete-event =  ã¤ãã³ããåé¤ãã
events.general.yes-delete-events = ã¤ãã³ããåé¤ãã
events.general.want-to-discard = ãã®ã¤ãã³ãã¸ã®å¤æ´ãããã¾ããããããã®å¤æ´ãåãæ¶ãã¾ããï¼
events.general.want-to-discard-title = å¤æ´ãåãæ¶ãã¾ããï¼
events.general.events-deleted = é¸æããã¤ãã³ããåé¤ããã¾ãã
events.general.no-hidden-events = éè¡¨ç¤ºã®ã¤ãã³ãã¯ããã¾ãã
events.general.no-visible-events = è¡¨ç¤ºå¯è½ãªã¤ãã³ãã¯ããã¾ãã
events.back-to-events = å¨ã¤ãã³ãã«æ»ã
events.general.error = ã¨ã©ã¼
events.no-segmentation = ã»ã°ã¡ã³ããªã
events.count = ã«ã¦ã³ã
events.sum = åè¨
events.dur = æé
events.table.count = ã«ã¦ã³ã
events.table.sum = åè¨
events.table.avg-sum = å¹³ååè¨
events.table.dur = æé
events.table.avg-dur = å¹³åæé
events.table.segmentation = ã»ã°ã¡ã³ãã¼ã·ã§ã³
events.edit.event-key = ã¤ãã³ãã­ã¼
events.edit.event-key-description = SDKããéä¿¡ãããããã¤ãã³ãã­ã¼
events.edit.event-name = ã¤ãã³ãå
events.edit.event-name-description = ã¦ã¼ã¶ã¼ã¤ã³ã¿ãã§ã¼ã¹å¨ä½ã§ä½¿ç¨ããããã®ã¤ãã³ãã­ã¼ã®è¡¨ç¤ºå
events.edit.event-description = èª¬æ
events.edit.event-description-description = A short description of this custom event and when it is triggered
events.edit.event-visibility = Event visibility
events.edit.event-visible = ã¤ãã³ããè¡¨ç¤º
events.edit.event-invisible = Event is invisible
events.edit.event-visible-description = ã¤ãã³ããéè¡¨ç¤ºã®å ´åããµã¼ãã¼ã¯ãã¼ã¿ãè¨é²ãã¾ãããã¦ã¼ã¶ã¼ã¤ã³ã¿ãã§ã¼ã¹ã«ã¯è¡¨ç¤ºããã¾ããã
events.edit.display-count = ã«ã¦ã³ãã®è¡¨ç¤ºå
events.edit.display-count-description = ãã®ã¤ãã³ãã®ã«ã¦ã³ããã­ããã£ã®è¡¨ç¤ºå
events.edit.display-sum = åè¨ã®è¡¨ç¤ºå
events.edit.display-sum-description = ãã®ã¤ãã³ãã®ä»»æã®åè¨ãã­ããã£ã®è¡¨ç¤ºå
events.edit.display-duration = æéã®è¡¨ç¤ºå
events.edit.display-duration-description = ãã®ã¤ãã³ãã®ä»»æã®æéãã­ããã£ã®è¡¨ç¤ºå
events.edit.event-properties = Event properties
events.no-event = ãã®ã¢ããªã±ã¼ã·ã§ã³ã«é¢ããã¤ãã³ãã®è¨é²ã¯ããã¾ãã
events.delete-confirm = ã¤ãã³ã"{0}"ã¨é¢é£ãããã¹ã¦ã®ãã¼ã¿ãã¯ãªã¢ãã¾ãã?
events.delete-confirm-many = ãã®ã¤ãã³ãã¨é¢é£ãããã¹ã¦ã®ãã¼ã¿ãã¯ãªã¢ãã¾ããï¼
events.delete.multiple-events = {0} events
events.edit.omit-event-segments = é¤å¤ããã¤ãã³ãã»ã°ã¡ã³ã
events.edit.omit-event-segments-description = Choose which segments of this custom event to omit. <b>Omitted segments will not be saved</b> in the future and past data for these segments will be purged immediately after you save these settings.
events.edit.omit-event-segments-description-drill = ãããã®ã»ã°ã¡ã³ãã®ãã¼ã¿ã¯ããªã«ã«ã¯ä¿å­ããã¾ãã
events.edit.omit-event-segments-to-omit = Segments to omit
events.edit.omit-event-select-segments = Select segments
event.edit.omitt-warning = ãã®ã¤ãã³ãã®ä¸é¨ã®ã»ã°ã¡ã³ãã®ãã¼ã¿ãé¤å¤ãã¾ããï¼é¤å¤ãããã»ã°ã¡ã³ãã¯ä»å¾ä¿å­ãããããããã®ã»ã°ã¡ã³ãã®éå»ãã¼ã¿ã¯è¨­å®ãä¿å­å¾ããã«åé¤ããã¾ããç¶è¡ãã¾ããï¼
events.overview.title = æ¦è¦
events.overview.drawer-title = è¨­å®ã®æ¦è¦
events.overview.empty = æ¦è¦ã«è¡¨ç¤ºããã¢ã¤ãã ãããã¾ãã
events.overview.configure = è¨­å®
events.overview.add-item=é ç®ã®è¿½å 
events.overview.added-items = è¿½å ããé ç®
events.overview.choose-event = ã¤ãã³ããé¸æ
events.overview.choose-property = ãã­ããã£ãé¸æ
events.overview.table.title-event = ã¤ãã³ã
events.overview.table.title-property = ãã­ããã£
events.overview.max-c = æ¦è¦ã«æå¤§12åã®ãã¬ãã¥ã¼ãè¿½å ã§ãã¾ããæ°ãããã®ãè¿½å ã§ããããã«ãåã«è¿½å ãããã®ãä¸é¨åé¤ãã¦ãã ããã
events.overview.have-already-one = æ¦è¦ã«åãã¤ãã³ãã¨ãã­ããã£ã®ã¢ã¤ãã ã1ã¤ããã¾ãã
events.overview.empty-title = ã¤ãã³ãæ¦è¦ãç©ºç½ã§ã
events.overview.empty-text-admin = ä¸ç®ã§æãéè¦ãªã«ã¹ã¿ã ã¤ãã³ããè¡¨ç¤ºã§ããããã«ã¤ãã³ãæ¦è¦ãè¨­å®ãã¾ãããã
events.overview.empty-text-user = ãã®ã¢ããªã±ã¼ã·ã§ã³ã®ã¢ããã³ããªã¯ã¨ã¹ããã¦ãæãéè¦ãªã¤ãã³ããä¸ç®ã§è¦ããããã«ã¤ãã³ãæ¦è¦ãè¨­å®ãã¦ãã ããã
events.overview.save-changes = å¤æ´ãä¿å­
events.overview.unknown = ãªã
events.all.empty-title = ãã®ã¢ããªã±ã¼ã·ã§ã³ã«ã¯ã«ã¹ã¿ã ã¤ãã³ããããã¾ãã
events.all.empty-text = SDKãä½¿ç¨ãã¦ã¢ããªã±ã¼ã·ã§ã³ã®ã³ã¼ãåã®ã«ã¹ã¿ã ã¤ãã³ããããã¤ãè¨é²ãã¦ãå¾ã§ãã®ã»ã¯ã·ã§ã³ã«æ»ã£ã¦ãã¦ãã ãã
events.top-events.title = ã«ã¦ã³ãå¥ã¤ãã³ã ä¸ä½
events.top-events.24hours = 24æé
events.top-events.30days = 30æ¥é
events.top-events.yesterday = Yesterday
events.top-events.info-text = {0}æéåã«æ´æ°æ¸ã¿
events.max-event-key-limit = Maximum limit of unique event keys ({0}) has been reached. Limit can be <a href="/dashboard#/manage/configurations/api">adjusted</a>.
events.max-segmentation-limit = Maximum limit of segmentations ({0}) in current event "{1}" has been reached. Limit can be <a href="/dashboard#/manage/configurations/api">adjusted</a>.
events.max-unique-value-limit = Maximum limit of unique values ({0}) in current event segmentation "{1}" has been reached. Limit can be <a href="/dashboard#/manage/configurations/api">adjusted</a>.

events.event-group-drawer-create = Create new Event Group
events.event-group-name = Event Group name
events.group-use-description = Use description
events.group-include-events = Include events
events.group-select-events = Select events to include
events.group-select-events-remind = Select events
events.group-visibility = Event Group Visibility
events.group-visibility-description = If an event group is invisible server will continue to record data for it but it will not be displayed in the user interface.
events.group-visibility-checkbox = Event Group is visible
events.group-invisibility-checkbox = Event Group is invisible
events.group-properties = Event Group Properties
events.event-group-count = Display name for count
events.event-group-count-description = A display name for the count property of this event
events.event-group-sum= Display name for sum
events.event-group-sum-description = A display name for the sum property of this event
events.event-group-duration = Display name for duration
events.event-group-duration-description = A display name for the duration property of this event
events.create-group = Create Event Group
events.save-group = Save Event Group
events.edit-your-group = Edit your Event Group

#export
export.export-as = ã¨ã¯ã¹ãã¼ã: 
export.export-number = åè¨{0}ä»¶ã®é ç®ã{1}åã®ã¨ã¯ã¹ãã¼ããã¡ã¤ã«ã«åå²ããã¾ãã
export.select = ã¨ã¯ã¹ãã¼ããããã¡ã¤ã«ãé¸æ
export.export = ã¨ã¯ã¹ãã¼ã
export.columns-to-export = Columns to export
export.export-all-columns = Export all columns
export.documents = ãã­ã¥ã¡ã³ã
export.export-columns-selected-count = {0}/{1} selected
export.format-if-possible = Format timestamps to readable date
export.format-if-possible-explain = Fields which are saved in data base as timestamps will be converted to show as date string like in table. For example: "Mon, 29 Jun 2020 17:14:15"
export.export-started = Export file is being generated. When ready you will see notification or can download it in report manager.
export.export-failed = Error upon attempting to export table data.
export.export-finished = Export completed.
export.export-finished-click = Click to download exported file.
export.file-name = File name

#management-applications
management-applications.title = Application Management
management-applications.my-new-app = æ°ãããã¤ã¢ããªã±ã¼ã·ã§ã³
management-applications.clear-data = ãã¼ã¿ãæ¶å»
management-applications.clear-reset-data = ã¢ããªã±ã¼ã·ã§ã³ããªã»ãã
management-applications.clear-reset-explanation = ã¢ããªãä½æç´å¾ã®ããã«åæã®ã¯ãªã¼ã³ãªç¶æã«ãªã»ãããã
management-applications.clear-all-data = ãã¹ã¦ã®ãã¼ã¿ãæ¶å»
management-applications.clear-all-explanation = åéãããã¼ã¿ãåé¤ãããè¨­å®ã¯ä¿æãã
management-applications.clear-1month-data = ï¼ã¶æä»¥åã®ãã¼ã¿ãæ¶å»
management-applications.clear-3month-data = ï¼ã¶æä»¥åã®ãã¼ã¿ãæ¶å»
management-applications.clear-6month-data = ï¼ã¶æä»¥åã®ãã¼ã¿ãæ¶å»
management-applications.clear-1year-data = ï¼å¹´ä»¥åã®ãã¼ã¿ãæ¶å»
management-applications.clear-2year-data = 2å¹´ä»¥åã®ãã¼ã¿ãæ¶å»
management-applications.yes-clear-app = ã¢ããªãã¼ã¿ãæ¶å»ãã
management-applications.no-clear = æ¶å»ããªã
management-applications.delete-an-app = Delete application
management-applications.yes-delete-app = ã¢ããªãåé¤ãã
management-applications.application-name = ã¢ããªã±ã¼ã·ã§ã³å
management-applications.application-name.tip = ã¢ããªã±ã¼ã·ã§ã³åãå¥å
management-applications.application-details = è©³ç´°ã®è¡¨ç¤º
management-applications.app-details = ã¢ããªã®è©³ç´°
management-applications.application-no-details = ã¢ããªã®è©³ç´°ãåå¾ã§ãã¾ããã§ãã
management-applications.type = ã¢ããªã±ã¼ã·ã§ã³ã¿ã¤ã
management-applications.type.tip = ã¢ããªã±ã¼ã·ã§ã³ã¿ã¤ãé¸æ
management-applications.type.hint = ãã®ã¢ããªã±ã¼ã·ã§ã³ã¿ã¤ãã®ãã¹ã¦ã®ãã¼ã¿ãè¨é²ããã¾ãã
management-applications.category = ã«ãã´ãª
management-applications.category.tip = ã«ãã´ãªãé¸æ
management-applications.app-id = App ID
management-applications.app-id.hint = ãã®IDã¯èª­ã¿åãAPIã§ä½¿ç¨ããã¾ãã
management-applications.app-key = ã¢ããªã±ã¼ã·ã§ã³ã­ã¼
management-applications.app-key.hint = SDKã®çµ±åã®ããããã®ã­ã¼ãå¿è¦ã«ãªãã¾ãã
management-applications.app-key.generate = Will generate automatically
management-applications.app-key-unique = This App Key is already in use
management-applications.time-zone = æéå¸¯
management-applications.time-zone.tip = å½ãé¸æ
management-applications.time-zone.hint = ãã¹ã¦ã®ãã¼ã¿ã¯ãã®ã¿ã¤ã ã¾ã¼ã³ã§è¨é²ããã¾ãã
management-applications.country.hint = City information will be only recorded for this country
management-applications.icon = App Icon
management-applications.add-application = ã¢ããªã±ã¼ã·ã§ã³ãè¿½å 
management-applications.clear-confirm-all = ã¢ããªã±ã¼ã·ã§ã³ã«ä¿å­ãããåéããããã¼ã¿ãå¨ã¦æ¶å»ãã¾ããç¶è¡ãã¾ããï¼
management-applications.clear-confirm-period = é¸æãããæéåã«ã¢ããªã±ã¼ã·ã§ã³åã«ä¿å­ãããåéããããã¼ã¿ãå¨ã¦æ¶å»ãã¾ããç¶è¡ãã¾ããï¼
management-applications.clear-confirm-reset = ã¢ããªãåæã®ã¯ãªã¼ã³ãªç¶æã«ãªã»ãããã¾ããç¶è¡ãã¾ããï¼
management-applications.clear-admin = ãã¼ã¿ã¯ã¢ããªã±ã¼ã·ã§ã³ç®¡çèã®ã¿ãåæåã§ãã¾ã
management-applications.clear-success = ã¢ããªã±ã¼ã·ã§ã³ãã¼ã¿ã®åæåãè¡ãã¾ãã
management-applications.reset-success = ã¢ããªã±ã¼ã·ã§ã³ãæ­£å¸¸ã«ãªã»ããããã¾ããã
management-applications.delete-confirm = ã¢ããªã±ã¼ã·ã§ã³ã«é¢é£ãããã¹ã¦ã®ãã¼ã¿ãåé¤ãã¾ããï¼
management-applications.delete-admin = ã¢ããªã±ã¼ã·ã§ã³ç®¡çèã®ã¿ãåé¤ã§ãã¾ã
management-applications.app-locked = ã¢ããªã±ã¼ã·ã§ã³ãã­ãã¯ããã¾ãã
management-applications.icon-error = å©ç¨å¯è½ãªç»åå½¢å¼ã¯jpg / png / gifã®ã¿ã§ã
management-applications.no-app-warning = ãã¼ã¿ã®ã³ãã¯ããéå§ããããã«ã¯ãã¾ãã¢ã«ã¦ã³ãã«ã¢ããªã±ã¼ã·ã§ã³ãè¿½å ããå¿è¦ãããã¾ã
management-applications.app-key-change-warning-title = ã¢ããªã­ã¼ãå¤æ´ãã
management-applications.app-key-change-warning = ã¢ããªã­ã¼ã®å¤æ´ã«ãã£ã¦ããã®æç¹ããå¨ã¦ã®ã¦ã¼ã¶ã¼ãããã¨ãã¢ããªã±ã¼ã·ã§ã³ãä»¥åä½¿ç¨ãã¦ããã¨ãã¦ãæ°è¦ã¦ã¼ã¶ã¼ã¨ãã¦è¨é²ããã¾ãã<br/><br/>ãã®ã¢ã¯ã·ã§ã³ã¯å¥ã®ãµã¼ãã¼ããã¢ããªã±ã¼ã·ã§ã³ãç§»è¡ãã¦ããå ´åãã¾ãã¯æ°ããã¢ããªã±ã¼ã·ã§ã³ã®ã¢ããªã­ã¼ãå¤æ´ãã¦ããå ´åã«ã®ã¿æ¨å¥¨ãã¾ãã
management-applications.app-key-change-warning-confirm = ç¶è¡ãããã¢ããªã­ã¼ãå¤æ´ãã
management-applications.app-key-change-warning-EE = ã¢ããªã­ã¼ã®å¤æ´ã«ãã£ã¦ããã®æç¹ããå¨ã¦ã®ã¦ã¼ã¶ã¼ãããã¨ãã¢ããªã±ã¼ã·ã§ã³ãä»¥åä½¿ç¨ãã¦ããã¨ãã¦ãæ°è¦ã¦ã¼ã¶ã¼ã¨ãã¦è¨é²ããã¾ãã<br/><br/>ãã®ã¢ã¯ã·ã§ã³ã¯å¥ã®ãµã¼ãã¼ããã¢ããªã±ã¼ã·ã§ã³ãç§»è¡ãã¦ããå ´åãã¾ãã¯æ°ããã¢ããªã±ã¼ã·ã§ã³ã®ã¢ããªã­ã¼ãå¤æ´ãã¦ããå ´åã«ã®ã¿æ¨å¥¨ãã¾ãã<br/><br/>ãã®ã¢ããªã±ã¼ã·ã§ã³ã®ãã¼ã¿åéãåæ­¢ããããã«ã¢ããªã­ã¼ãå¤æ´ãããå ´åã¯ããã£ã«ã¿ãªã³ã°ã«ã¼ã«ãã©ã°ã¤ã³ãä½¿ããã¨ãæ¨å¥¨ãã¾ãã
management-applications.first-app-message2 = Countly SDKãã¢ããªã«åãè¾¼ã¿ãã¹ãã¼ã¿ã¹ãããã«è¡¨ç¤ºã§ããããã«ãªãã¾ãããä¸ããApp Keyãåå¾ããã®ãå¿ããªãã§ãã ããã
management-applications.types.mobile = ã¢ãã¤ã«
management-applications.checksum-salt = ãã§ãã¯ãµã ã®ã½ã«ãå¤
management-applications.checksum-salt.hint = Will only accept requests where checksum is signed with the same salt in SDK
management-applications.app-domain = Website Domain
management-applications.app-creator = ã¢ããªä½æè
management-applications.app-created-at = ä½ææ¥
management-applications.app-edited-at = æçµæ´æ°
management-applications.app-last-data = Last data recorded (excluding Data Populator)
management-applications.app-users = æ¨©éã®ããã¦ã¼ã¶ã¼
management-applications.global_admins = ã°ã«ã¼ãã«ç®¡çè
management-applications.admins = ç®¡çè
management-applications.users = ã¦ã¼ã¶ã¼
management-applications.plugins = App settings
management-applications.plugins.save = ä¿å­
management-applications.plugins.smth = åé¡ãçºçãã¾ãã
management-applications.plugins.ok = äºè§£ãã¾ãã
management-applications.plugins.save.nothing = ä¿å­ããå¤æ´ã¯ããã¾ãã
management-applications.plugins.saved.nothing = ãã®ã¢ããªã±ã¼ã·ã§ã³ã«å¯¾ãã¦é©ç¨ããã¦ããå¤æ´ã¯ããã¾ãã
management-applications.plugins.saved.title = ãã©ã°ã¤ã³è¨­å®
management-applications.plugins.saved = å¤æ´ãæ­£ããä¿å­ããã¾ããï¼
management-applications.plugins.error.server = ä¸æãªãµã¼ãã¼ã¨ã©ã¼ã§ã
management-applications.create-first-app-title = æåã®ã¢ããªã±ã¼ã·ã§ã³ãè¿½å ãã¾ãããã
management-applications.create-first-app-description = æåã®ã¢ããªã±ã¼ã·ã§ã³ãè¿½å å¾ã«ãã¼ã¿åéãéå§ã§ããããã«ãªãã¾ãã
management-applications.contact-an-admin = ç®¡çèã«ãåãåãããã ãã
management-applications.dont-access = ã¢ããªã±ã¼ã·ã§ã³ã«å¯¾ããã¢ã¯ã»ã¹æ¨©éãããã¾ããã
management-applications.plugins-description = Settings in this section will override global settings for the application
management-applications.application-lock-tooltip = ã¢ããªã®ã­ãã¯ã«ããä¸æ³¨æã§ãã¼ã¿ãæ¶å»ãããã¨ããã¼ã¿ãå¥åããã®ãé²ããã¨ãã§ãã¾ãã
management-applications.application-tooltip-locked-text = App is locked. Unlock it to allow changes.
management-applications.application-tooltip-unlocked-text = App is unlocked. Lock it to restrict changes.

#management-users
management-users.read-permission-given-feature = Read permission granted automatically for
management-users.other-permissions-for = Other permissions for
management-users.removed-because-disabled = revoked automatically because of read permission disabled.
management-users.read-permission-all = Read permission automatically granted to all features.
management-users.other-permissions-removed = Other permissions revoked automatically because of read permission disabled.
management-users.permission-set-will-be-removed = Permission set will be removed
management-users.are-you-sure-to-continue = Are you sure to continue?
management-users.grant-user-access-to-apps = Grant User Access to Apps(s)
management-users.grant-user-access-to-apps-desc = Set user permissions in app and feature level
management-users.grant-admin-access-to-apps = Grant Admin Access to Apps(s)
management-users.grant-admin-access-description = User will have admin access to selected app(s)
management-users.add-permission-set = + Add Additional User Permission Set
management-users.profile-picture = Profile Picture
management-users.apps = Apps
management-users.sidebar-title = User Management
management-users.users = Users
management-users.view-title = Manage Users
management-users.editing-your-account = You\'re about to edit your own account. So you may be logged out automatically if changed something related with your permission. Do you want to contiue?
management-users.feature = Feature
management-users.create = Create
management-users.read = Read
management-users.update = Update
management-users.delete = Delete
management-users.edit-user = Edit settings
management-users.save-changes = Save Changes
management-users.create-new-user = Create new user
management-users.remove-permission-set = Remove permission set
management-users.create-user = Create user
management-users.discard-changes = Discard Changes
management-users.discard-confirm = You will lose all changes that you made. Are you sure to continue?
management-users.permission-settings = Permission settings
management-users.user-has-access-to = User has access to
management-users.role = å½¹å²
management-users.global-administrator = Global Administrator
management-users.full-name = ãã«ãã¼ã 
management-users.enter-full-name = Enter Full Name
management-users.username = ã¦ã¼ã¶ã¼å
management-users.enter-username = Enter Username
management-users.password = ãã¹ã¯ã¼ã
management-users.generate-password = ãã¹ã¯ã¼ããçæ
management-users.enter-password = Enter Password
management-users.generate-password = ãã¹ã¯ã¼ããçæ
management-users.change-password = ãã¹ã¯ã¼ããå¤æ´
management-users.role = å½¹å²
management-users.drag-and-drop-or = Drag and drop or
management-users.browser = browser
management-users.files-to-add-picture = files to add picture
management-users.pp-size-warning = JPG, PNG and GIF files allowed. Maximum size is 5 MB.
management-users.remove-image = Remove image
management-users.email = E-mail
management-users.enter-email = Enter E-mail address
management-users.global-admin = ã°ã­ã¼ãã«ç®¡çè
management-users.lock-account = ã¢ã«ã¦ã³ãã®ã­ãã¯
management-users.time-banned = ç¦æ­¢ãããæé
management-users.remove-ban = ç¦æ­¢ãåé¤
management-users.remove-ban-notify-title = æéç¦æ­¢ãåé¤ããã¾ããã
management-users.remove-ban-notify-message = ã¦ã¼ã¶ã¼ã®æéç¦æ­¢ãåé¤ããã¾ããã
management-users.admin = ç®¡çè
management-users.admin-of = ç®¡çè: 
management-users.admin-of.tip = ã¦ã¼ã¶ã¼ã¯ã¢ããªã±ã¼ã·ã§ã³ç®¡çèã¨ãã¦è¨­å®ããã¦ãã¾ãã
management-users.user = ã¦ã¼ã¶ã¼
management-users.user-of = ã¦ã¼ã¶ã¼: 
management-users.user-of.tip = ã¦ã¼ã¶ã¼ã¯ã¢ããªã±ã¼ã·ã§ã³è¡¨ç¤ºã®æ¨©éãæã£ã¦ãã¾ãã
management-users.no-role = å½¹å²ãªã
management-users.create-user = Create user
management-users.delete-user = ã¦ã¼ã¶ã¼ãåé¤
management-users.edit = ã¯ãªãã¯ãã¦ç·¨é
management-users.all-roles = All roles
management-users.not-logged-in-yet = Not logged in yet
management-users.close = ã¯ãªãã¯ãã¦éãã
management-users.password-change-confirm = You have changed {0}\''s password. Do you want a notification email to be sent?
management-users.delete-confirm = You are about to delete {0}\''s account. Do you want to continue?
management-users.delete-confirm-title = ã¦ã¼ã¶ã¼ãåé¤ãã¾ããï¼
management-users.yes-delete-user  = ã¦ã¼ã¶ã¼ãåé¤ãã
management-users.email.invalid = ã¡ã¼ã«ã¢ãã¬ã¹ãæ­£ããããã¾ãã
management-users.email.exists = å­å¨ããã¡ã¼ã«ã¢ãã¬ã¹
management-users.username.exists = å­å¨ããã¦ã¼ã¶ã¼å
management-users.password.length = ãã¹ã¯ã¼ãã¯æä½ {0} æå­å¿è¦ã§ã
management-users.password.has-char = ãã¹ã¯ã¼ãã«å¤§æå­ãæä½1æå­å«ãã
management-users.password.has-number = ãã¹ã¯ã¼ãã¯æä½1æå­æ°å­ãå¥åãã¦ãã ãã
management-users.password.has-special = ãã¹ã¯ã¼ãã¯æä½1æå­ç¹æ®æå­(?_*#!-$@,. etc)ãå¥åãã¦ãã ãã
management-users.add-user = ã¦ã¼ã¶ã¼è¿½å 
management-users.revoke-access = ã¢ã¯ã»ã¹åãæ¶ã
management-users.admin-user-alert = ã¦ã¼ã¶ã¼ç»é²ãå¿è¦ã§ãã
management-users.revoke-confirm = ã¦ã¼ã¶ã¼ç»é²ãåé¤ãã¾ãã?
management-users.email-tip = Eã¡ã¼ã«ãã¢ã«ã¦ã³ãã§ã¯ãªãå ´åã¯ãã¤ãã³ããç¥ãããåä¿¡åºæ¥ã¾ãã
management-users.last_login = æçµã­ã°ã¤ã³
management-users.created = ä½æ
management-users.created-message = User created successfully\!
management-users.removed = Removed
management-users.removed-message = User removed successfully\!
management-users.remove-canceled = User remove canceled
management-users.updated = Updated
management-users.updated-message = User informations updated successfully\!
management-users.confirm-loss-checkboxes = You will lose all marked permissions below. Are you sure you want to continue?
management-users.select-app-first-title = Select app first
management-users.select-app-first-message = You need to select app or apps before you mark permission boxes
management-users.full-name-required = Full name area should be filled
management-users.username-required = Username area should be filled
management-users.email-required = E-mail area should be filled
management-users.email-invalid-format = Please check your email format
management-users.at-least-one-app-required = User should have permission for at least one app
management-users.view-user-logs = View user logs
management-users.this-will-delete-user = This will permanently delete the user. Continue?
management-users.warning = Warning
management-users.future-plugins = User will have this access type to all additional features that might be enabled in the future.
management-users.all-groups = All groups
management-users.group = Group
management-users.group-blank = -
management-users.reset-filters = Reset Filters
management-users.search-placeholder = Search in Features
management-users.reset-failed-logins = Reset failed logins
management-users.reset-failed-logins-success = Failed logins reset successfully\!
management-users.reset-failed-logins-failed = Failed to reset logins\!

#user-settings
user-settings.username = ã¦ã¼ã¶ã¼å
user-settings.change-password = ãã¹ã¯ã¼ããå¤æ´ãã
user-settings.old-password = å¤ããã¹ã¯ã¼ã
user-settings.new-password = æ°ãããã¹ã¯ã¼ã
user-settings.password-again = ããä¸åº¦
user-settings.alert = ä½ããééã£ã¦ãã¾ã
user-settings.success = è¨­å®ãä¿å­ããã¾ãã
user-settings.api-key = APIã­ã¼
user-settings.password-match = ãã¹ã¯ã¼ããæ­£ããããã¾ãã
user-settings.old-password-match = åã®ãã¹ã¯ã¼ããå¥åãã¦ãã ãã
user-settings.old-password-not-match = å¤ããã¹ã¯ã¼ããä¸è´ãã¾ãã
user-settings.password-not-old = æ°ãããã¹ã¯ã¼ãã«å¤ããã¹ã¯ã¼ããä½¿ç¨ããªãã§ãã ãã
user-settings.force-password-reset = ãã¹ã¯ã¼ãã®å¤æ´ããå§ããã¾ã
user-settings.please-correct-input = ãã©ã¼ã ãéä¿¡ããåã«ã¨ã©ã¼ãä¿®æ­£ãã¦ãã ãã
user-settings.api-key-length = APIã­ã¼ã¯32æå­ã§ã
user-settings.api-key-restrict = API key should contain digits and alphabetical characters
user-settings.regenerate-api-key = APIã­ã¼ãåçæ
user-settigs.delete-account = ã¢ã«ã¦ã³ãã®åé¤
user-settings.delete-account-title =  ã¢ã«ã¦ã³ããåé¤ãã¾ããï¼
user-settings.delete-account-confirm = ã¢ã«ã¦ã³ããæ¬å½ã«åé¤ãã¾ããï¼åé¤ããã¨åã«æ»ããã¨ã¯ã§ãã¾ããã
user-settings.password-mandatory = ãã¹ã¯ã¼ãã¯å¿é ã§ãï¼
user-settings.global admin limit = ãã®ã¢ã«ã¦ã³ãã¯æå¾ã®ã°ã­ã¼ãã«ç®¡çã¢ã«ã¦ã³ãã§ããæå¾ã®ã°ã­ã¼ãã«ç®¡çã¢ã«ã¦ã³ãã¯åé¤ã§ãã¾ããã
user-settings.password not valid = å¥åããããã¹ã¯ã¼ããæå¹ã§ã¯ããã¾ãã
user-settings.profile-picture = ãã­ãã£ã¼ã«åç
user-settings.upload = Upload

#report manager
report-manager.name = åå
report-manager.desc = èª¬æ
report-manager.name-and-desc = ååããã³èª¬æ
report-manager.data = ãã¼ã¿
report-manager.period = æé
report-manager.visibility = è¡¨ç¤º
report-maanger.manually-created = ã¦ã¼ã¶ã¼ä½æ
report-maanger.automatically-created = èªåä½æ
report-maanger.manually-created-title = ã¦ã¼ã¶ã¼ãä½æããã¬ãã¼ã
report-maanger.automatically-created-title = èªåä½æãããã¬ãã¼ã
report-manager.all-origins = All Origins
report-manager.all-types = All Types
report-manager.all-statuses = All Statuses
report-manager.all-owners = All Owners
report-manager.my-reports = My Reports
report-manager.all-sources = All Data Sources
report-manager.data-source = Data source
report-manager.select-data-source = Select data source
report-manager.runtime-type = Runtime type
report-manager.origin = Origin
report-manager.select-origin = Select origin
report-manager.owner = Owner
report-manager.status = Status
report-manager.app-independent = App Independent
report-manager.reset-filters = Reset filters
report-manager.filters = Filters

#app-users
app-users.download-debug-info = Download debug information
app-users.export-userdata = ã¦ã¼ã¶ã¼ã®ãã¼ã¿ãã¨ã¯ã¹ãã¼ã
app-users.download-export = ã¦ã¼ã¶ã¼ã®ã¨ã¯ã¹ãã¼ãããããã¼ã¿ã®ãã¦ã³ã­ã¼ã
app-users.delete-export = ã¦ã¼ã¶ã¼ã®ã¨ã¯ã¹ãã¼ãããããã¼ã¿ãåé¤
app-users.delete-userdata = ã¦ã¼ã¶ã¼ã®ãã¼ã¿ãå®å¨ã«åé¤
app-users.delete-userdata-confirm = ãã®ã¦ã¼ã¶ã¼ã«é¢é£ä»ãããã¦ããå¨ã¦ã®ãã¼ã¿ãåé¤ãã¾ããï¼
app-users.export-started = ã¨ã¯ã¹ãã¼ããéå§ããã¾ããã
app-users.export-finished = ã¦ã¼ã¶ã¼ãã¼ã¿ãã¨ã¯ã¹ãã¼ãããã¾ãããä»ãããã¦ã³ã­ã¼ãã§ãã¾ãã
app-users.export-finished-click = ãããã¯ãªãã¯ãã¦ãã¦ã³ã­ã¼ããã¦ãã ããã
app-users.export-failed = ã¨ã¯ã¹ãã¼ããã­ã»ã¹ä¸­ã«ã¨ã©ã¼ãããã¾ãããè©³ç´°ãªæå ±ã¯ã­ã°ã«è¨é²ããã¦ããå¯è½æ§ãããã¾ãã
app-users.export-deleted = ã¨ã¯ã¹ãã¼ããã¼ã¿ãåé¤ããã¾ãã
app-users.userdata-deleted  = ã¦ã¼ã¶ã¼ãã¼ã¿ãåé¤ããã¾ãã
app-users.yes-purge-data = ãã¼ã¿ãåé¤ãã
app-users.no-dont-purge = åé¤ããªã
app-users.purge-confirm-title = ãã¼ã¿ãåé¤ãã¾ããï¼
app-users.debug-options = Debug options

#downloading-view
downloading-view.download-title = ãã¦ã³ã­ã¼ãã¯ããã«éå§ãã¾ã
downloading-view.if-not-start = ãã¦ã³ã­ã¼ããèªåçã«éå§ããªãå ´åã¯ãããã¯ãªãã¯ãã¦ãã ãã
downloading-view.download-not-available-title = ãã¦ã³ã­ã¼ããéå§ã§ãã¾ãã
downloading-view.download-not-available-text = ãã®ãªã³ã¯ã«æ¥ç¶ããã¦ããã¨ã¯ã¹ãã¼ããããã¾ããã§ãããã¨ã¯ã¹ãã¼ãã¯ãã§ã«åé¤ããã¦ããå¯è½æ§ãããã¾ãã

#token-manager
token_manager.page-title = ãã¼ã¯ã³ããã¼ã¸ã£ã¼
token_manager.create-token = Create Token
token_manager.create-new-token = Create New Token
token_manager.table.id = ãã¼ã¯ã³ID
token_manager.table.ends =æ¬¡ã®ææ¥ã¾ã§æå¹: 
token_manager.table.multi  =   è¤æ°å
token_manager.table.owner = ãã¼ã¯ã³ã®ææè
token_manager.table.app = ã¢ããª
token_manager.table.status  = ã¹ãã¼ã¿ã¹
token_manager.table.endpoint = Endpoint
token_manager.table.endpoints = Endpoints
token_manager.table.endpoint-name = Endpoint Name
token_manager.table.endpoint-detail = ENDPOINT DETAILS
token_manager.table.endpoints-description = Given endpoints are interpreted as regularexpressions
token_manager.table.expiration-description = Set expiration time for token
token_manager.table.purpose = èª¬æ
token_manager.table.token-description = Token Description
token_manager.table.purpose-desc = ä½æããããã¼ã­ã³ã®å¤å¥ã«å½¹ç«ã¤æå ±ã§ãã
token_manager.table.endpoint-desc = ãã¼ã¯ã®ã1ã¤ã¾ãã¯è¤æ°ã®ã¨ã³ããã¤ã³ãã«å¶éãããã¨ãã§ãã¾ãã<br/>ä¸ããããã¨ã³ããã¤ã³ãã¯éå¸¸ã®è¡¨ç¾ã¨ãã¦è§£éããã¾ãã
token_manager.table.multi-desc = ãã¼ã¯ã³ã¯è¤æ°åä½¿ç¨ã§ãã¾ã
token_manager.table.apps-title = Token Usage
token_manager.table.apps-limit = ãã¼ã¯ã³ãä¸é¨ã®ã¢ããªã§ã®ã¿ä½¿ç¨ããããã«è¨±å¯
token_manager.table.apps-allow = ãã¼ã¯ã³ãå¨ã¦ã®ã¢ããªã§ä½¿ç¨ã§ããããã«ãã
token_manager.table.limit-limit.label =  Limited Time
token_manager.table.limit-limit.text = Token will expire after the time you set
token_manager.table.limit-allow.label =  Unlimited Time
token_manager.table.limit-allow.text = Token can be used until it is deleted
token_manager.table.limit-title = Token Expiration
token_manager.table.enter-number = æ°ãå¥å
token_manager.table.not-expire = æéæºäºã§ã¯ãªã
token_manager.table.all-apps = å¨ã¢ããª
token_manager.limit.h = æé
token_manager.limit.d = æ¥
token_manager.limit.m = æ
token_manager.delete-token-confirm = ãã®ãã¼ã¯ã³ãåé¤ãã¾ããï¼
token_manager.delete-token-confirm-title = ãã¼ã¯ã³ãåé¤ãã¾ããï¼
token_manager.yes-delete-token = ãã¼ã¯ã³ãåé¤ãã
token_manager.delete-error = ãã¼ã¯ã³ã®åé¤ã«å¤±æãã¾ãã
token_manager.select-apps-error = ã¢ããªãé¸æããããªãã·ã§ã³ãããã¼ã¯ã³ãå¨ã¦ã®ã¢ããªã§ä½¿ç¨ã§ããããã«ãããã«è¨­å®ããå¿è¦ãããã¾ã
token_manager.select-expire-error = ãã¼ã¯ã³ã®æå¹æéãé¸æãããããªãã·ã§ã³ãããã¼ã¯ã³ãåé¤ããã¾ã§ä½¿ç¨ã§ãããã«è¨­å®ããå¿è¦ãããã¾ã
token_manager.table.delete-token = ãã¼ã¯ã³ãåé¤
token_manager.table.status-expired = Expired
token_manager.table.status-active = Active
token_manager.copy-token = ã¯ãªãã¯ãã¦ã³ãã¼
token_manager.token-coppied = ãã¼ã¯ã³ãã³ãã¼ããã¾ãã
token_manager.query-param = Query Parameters
token_manager.query-param-value = å¤
token_manager.query-param-desc = ä¸é¨ã®ãã©ã¡ã¼ã¿ã¼å¤ã«ããå¶é (ä¾: method=get_events) ãå¤ã¯èªè¨¼æã«æ­£è¦è¡¨ç¾ã¨ãã¦ä½¿ç¨ããã¾ãã
token_manager.add-new-endpoint = æ°ããã¨ã³ããã¤ã³ããè¿½å 
token_manager.add-param = ãã©ã¡ã¼ã¿ãè¿½å 
token_manager.parameter  = ãã©ã¡ã¼ã¿
token_manager.select-apps  = Select Apps
token_manager.select-time-unit = æéåä½ãé¸æ
token_manager.token-expiration-time = Expiration Time
token_manager.LoginAuthToken-description  =  This token is created when creating dashboard screenshots. <br/>If you are not currently rendering dashboard images, you can delete this token.
token_manager.LoggedInAuth-description = This token is used for keeping users session.<br/> Deleting it will log out user currently using it to keep session.


version_history.page-title = Countlyãã¼ã¸ã§ã³å±¥æ­´
version_history.current-version = ç¾è¡ãã¼ã¸ã§ã³
version_history.package-version = Package version
version_history.version = ãã¼ã¸ã§ã³
version_history.upgraded = ã¢ããã°ã¬ã¼ãæ¸ã¿/ã¤ã³ã¹ãã¼ã«æ¸ã¿
version_history.alert-title = Version mismatch
version_history.alert-message = There is a version mismatch between version in files and in database. It may indicate that upgrade scripts didn't complete properly.
internal-events.[CLY]_session = ã»ãã·ã§ã³

#jobs
jobs.back-to-jobs-list = Back to jobs list
jobs.job-name = Name
jobs.job-schedule = Schedule
jobs.job-next-run = Next Run
jobs.job-last-run = Last Run
jobs.job-status = Status
jobs.job-total-scheduled = Total
jobs.job-data = Data
jobs.run-duration = Duration
jobs.suspend = Suspend
jobs.suspend-successful-message = {0} suspended successfully
jobs.schedule = Schedule

systemlogs.action.task_manager_task_deleted = Report deleted
systemlogs.action.task_manager_task_updated = Report updated
systemlogs.action.task_manager_task_created = Report created

#events-overview
events.overview.title.new = Events Overview
events.overview.title.new.tooltip = Complete summary of all Events being tracked.
events.overview.total.event.count = Total number of Events triggered in the last 30 days.
events.overview.event.per.user = Average number of Events triggered per user, in the last 30 days.
events.overview.event.per.session = Average number of Events triggered per session, in the last 30 days.
events.overview.event.metrics = Overview of the metrics calculated by the identified Events, in the last 30 days.
events.overview.event.monitor.events = A quick summary of selected Events that you wish to monitor. To select Events that you want to highlight here, please click on 'Configure Events'.
events.overview.metrics = Event Metrics
events.overview.monitor = Monitor Events
events.overview.event = EVENT
events.overview.segment = SEGMENT NAME
events.overview.count = Count
events.overview.sum = Sum
events.overview.duration = Duration
events.overview.dur = Dur
events.overview.time.period = TIME PERIOD
events.percentage.of.total = % of Total
events.overview.top.events.by.count = Top Events by Count in the last 30 days
events.overview.updated = Updated
events.overview.total.events.count = Total Event Count
events.overview.events.per.session = Events Per Session
events.overview.events.per.user = Events Per User
events.overview.events.configure.events = Configure Events
events.overview.add.item = Add Item
events.overview.add.to.list = Add to List
events.overview.manage.items = Manage Items
events.overview.last-30days = Last 30 Days

#all-events
events.all.title.tooltip = Details of the stats of each Event, in the selected time period, and their comparison where applicable.
events.all.title.new = All Events
events.all.period = PERIOD
events.all.segmentation = SEGMENTATION BY
events.all.search.placeholder = Search in {0} Events
events.all.group = GROUP
events.all.error = Could not fetch data
events.all.count = Count
events.all.sum = Sum
events.all.duration = Duration
events.all.any.segmentation = Any segmentation
events.all.omitted.segments = OMITTED

#auto-refresh
auto-refresh.help= Automatically refresh can be adjusted through this switch
auto-refresh.enable= Enable Auto-refresh
auto-refresh.stop= Stop Auto-refresh
auto-refresh.is= Auto-refresh is
auto-refresh.enabled = Enabled

initial-setup.application = Application
initial-setup.add-first-application-title = Let's add your first application
initial-setup.add-first-application-byline = After adding your first application, you'll be ready to start collecting data
initial-setup.add-demo-application-title = Let's create a demo app for you!
initial-setup.application-type-label = Select your application type
initial-setup.time-zone-label = Select your time zone
initial-setup.application-sample-label = We'll populate some demo data for your app, which example sounds the best?
initial-setup.create-application = Create Application
initial-setup.continue-data-population = Continue with data population
initial-setup.consent-title = Before we start...
initial-setup.analytics-blurb-1 = We utilize Countly to understand user interactions and collect feedback, helping us enhance our product continuously. However, your privacy remains our priority.
initial-setup.analytics-blurb-2 = This analysis is done on the server level, so we won't see or collect any individual details or any data you record. The data is reported back only to our dedicated Countly server based in Europe. Please note, you can change your mind at any time in the settings.
initial-setup.analytics-question = Considering our commitment to maintaining your privacy and the potential benefits for product enhancement, would you be comfortable enabling Countly on this server?
initial-setup.analytics-no = No, maybe later
initial-setup.analytics-yes = Yes, enable Countly on this server
initial-setup.newsletter-blurb = We offer a newsletter brimming with recent updates about our product, news from Countly, and information on product analytics. We assure you - our aim is to provide value and insights, not clutter your inbox with unwanted emails.
initial-setup.newsletter-question = Would you be interested in subscribing to our newsletter?
initial-setup.newsletter-no = No, thank you.
initial-setup.newsletter-yes = Yes, subscribe me to the newsletter
initial-setup.quickstart-title = Quick Start Guide
