#common
common.total-sessions = Total Sessions
common.total-sessions-description = Number of times your application is opened, by new or returning users, in the selected time period.
common.new-sessions = New Sessions
common.new-sessions-description = Number of times your application is opened by a new user, in the selected time period. It is equal to the number of New Users and it only counts the first session the user had.
common.unique-sessions = Unique Sessions
common.unique-sessions-description = Number of times your application is opened by a new or returning user from a unique device, in the selected time period. It is equal to the number of Total Users.
common.total-users = TOTAL UTILISATEURS
common.new-users = NOUVEAUX UTILISATEURS
common.returning-users = UTILISATEURS CONNUS
common.date = Date
common.month = Month
common.percent = Pourcentage
common.number-of-users = Nombre d'Utilisateurs
common.number-of-sessions = Number of Sessions
common.session-duration = Session Duration
common.table.no-data = Aucune donnÃ©e
common.search.no-match-found = No match found
common.table.total-users = Total Utilisateurs
common.table.total-users-desc = The number of users (unique devices/IDs) who have opened your application in the selected time period.
web.common.table.total-users = Total Visitors
web.common.table.total-users-desc = The number of visitors (unique devices/IDs) who have visited your website in the selected time period.
common.table.new-users = Nouveaux Utilisateurs
common.table.new-users-desc = The number of first-time users (unique devices/IDs) in the selected time period.
common.table.returning-users = Utilisateurs Connus
common.table.returning-users-desc = Number of users using your application for the second or later time, in the selected time period, calculated as Total Users (less) New Users.
web.common.table.returning-users = Returning Visitors
web.common.table.returning-users-desc = Number of visitors using your website for the second or later time, in the selected time period, calculated as Total Visitors (less) New Visitors.
common.table.total-sessions = Total Sessions
common.table.new-sessions = Nouvelles Sessions
common.table.unique-sessions = Sessions Uniques
common.today = Aujourd'hui
common.7days = 7 jours
common.30days = 30 jours
common.60days = 60 jours
common.90days = 90 jours
common.from = from
common.to  = to
common.download = Download
common.refresh = Refresh
common.compare-to = Compare to
common.no-comparison = No comparison
common.of-total = of Total
common.comparison-desc = Select comparison type to show past data on the same graph
common.yesterday = Yesterday
common.previous-period = Previous period
common.previous-year = Same period in previous year
common.bar.top-resolution = Top Resolutions
common.bar.top-resolution.description = Top 5 resolution settings of the devices used your users' sessions, in the selected time period.
web.common.bar.top-resolution.description = Top 5 resolution settings of the devices used your visitors' sessions, in the selected time period.
common.bar.top-carrier = Top Carriers
common.bar.top-users = TOP UTILISATEURS
common.bar.top-platform = Top Platforms
common.bar.top-platform.description = Top 5 versions of the platforms of your usersâ sessions, in the selected time period.
web.common.bar.top-platform.description = Top 5 versions of the platforms of your visitorsâ sessions, in the selected time period.
common.bar.top-platform-version = Top Platform Versions
common.bar.top-platform-version.description = Top 3 versions of the platforms of your users' sessions, in the selected time period.
web.common.bar.top-platform-version.description = Top 3 versions of the platforms of your visitors' sessions, in the selected time period.
common.bar.top-devices = Top Devices
common.bar.top-devices.description = Top 5 devices of your usersâ based on their sessions, in the selected time period.
web.common.bar.top-devices.description = Top 5 devices of your visitorsâ based on their sessions, in the selected time period.
common.bar.top-device-types = Top Device types
common.bar.top-device-types.description = Top 5 device types of your usersâ based on their sessions, in the selected time period.
web.common.bar.top-device-types.description = Top 5 device types of your visitorsâ based on their sessions, in the selected time period.
common.bar.top-browsers = Top Browsers
common.bar.top-browsers.description = Top 5 browsers of your usersâ based on their sessions, in the selected time period
common.bar.top-app-versions = Top App Versions
common.bar.top-app-versions.description = Top 5 App versions of your usersâ based on their sessions, in the selected time period.
web.common.bar.top-app-versions.description = Top 5 App versions of your visitorsâ based on their sessions, in the selected time period.
common.bar.no-data = No Data
common.apply = Appliquer
common.apply-changes = Apply changes
common.cancel = Annuler
common.select = Select
common.select-all = SÃ©lectionner tout
common.deselect-all = DÃ©sectionner tout
common.all = All
common.select-type = Select type
common.yes = Oui
common.no = Non
common.done = Fait
common.save = Sauver
common.saving = Sauvegarde
common.close = Fermer
common.copy-id = Copy widget ID
common.loading = Loading
common.continue = Continuer
common.okay = Okay
common.add = Ajouter
common.delete = Supprimer
common.edit = Ãditer
common.lock = Lock
common.locked = Locked
common.unlock = Unlock
common.unlocked = Unlocked
common.ok = OK
common.total = Total
common.status = Status
common.select-status = Select status
common.select-type = Select type
common.type = Type
common.time = Time
common.started = Started
common.info = Information
common.save-changes = Modifications enregistrÃ©es
common.graph-max = Maximum <b>{0}</b> {1} atteint sur <b>{2}</b>
common.graph-min = Minumum <b>{0}</b> {1} sur <b>{2}</b>
common.graph.time-spent = Temps PassÃ© (min)
common.graph.average-time = Moy. Temps PassÃ© (min)
common.graph.reqs-received = RequÃªtes ReÃ§ues
common.graph.avg-reqs-received = Moy. RequÃªtes ReÃ§ues
common.graph.no-data = There is no data to show for this period
common.seconds = secondes
common.minutes = minutes
common.hour = heure
common.second.abrv = s
common.second.abrv2 = s
common.minute.abrv = m
common.minute.abrv2 = m
common.hour.abrv = h
common.hour.abrv2 = h
common.day.abrv = jours
common.day.abrv2 = day
common.year.abrv = annÃ©es
common.year.abrv2 = yr
common.buckets.monthly = mensuel
common.buckets.weekly = hebdomadaire
common.buckets.daily = journalier
common.buckets.years = years
common.buckets.months = mois
common.buckets.weeks = semaines
common.buckets.days = jours
common.max = Max
common.create = CrÃ©er
common.session-expiration = Session expiration
common.click-to-login = Click to remain login
common.expire-minute = Your session will expire in 1 minute
common.expire-seconds = Your session will expire in 10 seconds
common.add-new-app = Add new app here
common.add-new-app-button = Add new app
common.change-app-type = change app type here
common.install-plugin = Please Install plugin here
common.or = or
common.missing-type = Dashboard is missing for type
common.feedback-and-support = Feedback & Support
common.provide-feedback = Provide Feedback
common.support = Support
common.documentation = Documentation
common.help-center = Help Center
common.feature-request = Request a Feature
common.showing = Showing _START_ to _END_ of _TOTAL_ entries
common.filtered = (filtered from _MAX_ total entries)
common.enterprise-edition = Countly Enterprise (supported)
common.save-to-csv = Save to CSV
common.save-to-excel = Save for Excel
common.search = Search
common.unknown = Unknown
common.eu = European Union
common.integrate-sdks = Need some help with SDK integration?
common.integrate-sdks-text = helps you generate personalized code snippets and SDK integration tutorials based on your platforms and Countly features you want to use.
common.integrate-sdks-platforms = Select a platform to get started
common.go-to-countries = Go to Countries
common.show = Show
common.show-items = Display
common.items-per-page = Items per page
common.try-later = Can't perform this operation now, try again later.
common.never = Never
common.in.days = in {0} days
common.in.hours = in {0} hours
common.in.minutes = in {0} minutes
common.in.seconds = in {0} seconds
common.ago.just-now = just now
common.ago.seconds-ago = {0} seconds ago
common.ago.half-minute = half a minute ago
common.ago.less-minute = less than a minute ago
common.ago.one-minute = one minute ago
common.ago.minutes-ago = {0} minutes ago
common.ago.one-hour = 1 hour ago
common.ago.hours-ago = {0} hours ago
common.ago.one-day = 1 day ago
common.ago.days-ago = {0} days ago
common.ago.one-week = 1 week ago
common.every.minutes = every {0} minutes
common.every.hours = every {0} hours
common.every.hour = every hour
common.estimation = Total (unique) value for this period is estimated and corrected using the biggest time buckets from available daily, weekly and monthly stats.<br/><br/>Exact total counts are available for this year, month and day periods
common.action = Action
common.view = View
common.back = Back
common.success = Success
common.error = Error
common.no-clear = No don't clear
common.yes-discard = Yes, discard
common.yes-clear-it = Yes, clear it
common.no-dont-change = No, don't change
common.no-dont-delete = No, don't delete
common.no-dont-continue = No, don't continue
common.no-dont-do-that  = No, don't do that
common.last-updated = Last updated
common.location = Location
common.enable = Enable
common.running = Running
common.completed = Completed
common.stopped = Stopped
common.errored = Error
common.visits = Visits
common.select-columns-to-display = Select columns to display
common.enter-value = Enter value
common.optional = Optional
common.clear-values = Clear values
common.confirm = Are you sure?
common.json-editor = JSON Editor
common.valid-json = Valid JSON code
common.invalid-json = Invalid JSON code
common.format = Format
common.saveJSON = Save JSON
common.zoom-in = Zoom
common.cancel-zoom = Cancel zoom
common.zoom-out = Zoom out
common.zoom-reset = Reset Zoom
common.zoom-info = Select an area in the chart to zoom in
common.group = GROUP
common.send = Send
common.customize = Customize
common.breakdown-not-available = Breakdown is not available
common.breakdown-not-available-desc = Breakdown is not available for given time period. There is no data.
common.internal-events = Internal Events
common.custom-events = Custom Events
common.none = None
common.emtpy-view-title = ...hmm, seems empty here
common.emtpy-view-subtitle = No data found
common.no-widget-text = Add a widget to create dashboard and see <br/> some informative explanation
common.no-dashboard-text = Create dashboard and see<br/> some informative explanation
common.create-dashboard = + Create a Dashboard
common.add-widget = + Add a Widget
common.sunday = Sunday
common.monday = Monday
common.tuesday = Tuesday
common.wednesday = Wednesday
common.thursday = Thursday
common.friday = Friday
common.saturday = Saturday
common.hours = Hours
common.chart-type = Chart Type
common.adjust-limit = Adjust Limit
common.copy-error-message = Something went wrong, please copy it again.
common.created-at-by = Created <span>{0}</span> by {1}
common.updated = Updated

#vue
common.undo = Undo
common.drawer.next-step = Next step
common.drawer.previous-step = Previous step
common.diff-helper.changes = You made {0} changes.
common.diff-helper.keep = Do you want to keep them?
common.save-changes = Modifications enregistrÃ©es
common.discard-changes = Discard
common.complete = Complete
common.reset = Reset
common.confirm = Are you sure?
common.remove = Remove
common.next = Next
common.previous = Previous
common.select-at-least = At least {0} items should be selected.
common.select-at-most = At most {0} items can be selected.
common.and = and
common.or = or
common.enter-email-addresses=Enter Email Addresses
common.invalid-email-address="{0}" is not a valid email address
common.email-example=(e.g. <EMAIL>)
common.no-email-addresses=No addresses have been specified
#period-picker
common.all-time = All time
common.in-last-days-plural = in the last {0} days
common.in-last-days = in the last day
common.in-last-weeks-plural = in the last {0} weeks
common.in-last-weeks = in the last week
common.in-last-months-plural = in the last {0} months
common.in-last-months = in the last month
common.time-period-select.custom-range = Custom Range
common.time-period-select.last-n = In the Last
common.time-period-select.since = Since
common.time-period-select.on = On
common.time-period-select.range = In Between
common.time-period-name.since = since {0}
common.time-period-name.on = on {0}
common.time-period-select.before = Before
common.time-period-name.before = Before {0}
common.time-period-name.range = {0} - {1}
common.apply-range = Apply Range
common.range-length-limit = Selected range cannot be longer than {0}.

#taskmanager
taskmanager.rerun = Rerun
taskmanager.stop = Stop
taskmanager.view-old = View Cache
taskmanager.confirm-delete-title = Delete report?
taskmanager.confirm-stop = Are you sure you want to stop this task?
taskmanager.confirm-delete = Are you sure you want to delete report called {0}?
taskmanager.confirm-rerun = Are you sure you want to rerun this task?
taskmanager.confirm-rerun-title = Rerun task?
taskmanager.confirm-stop-title = Stop task?
taskmanager.yes-rerun-task = Yes, rerun this task
taskmanager.yes-stop-task = Yes, stop this task
taskmanager.yes-delete-report = Yes, delete report
taskmanager.rerunning = Re-Running
taskmanager.select-origin = Select origin
taskmanager.origin = Origin
taskmanager.auto = Auto refresh
taskmanager.manual = One time
taskmanager.edit = Edit
taskmanager.last-today = Today
taskmanager.last-7days = Last 7 days
taskmanager.last-30days = Last 30 days
taskmanager.last-60days = Last 60 days
taskmanager.manually-table-remind = One-time and auto-refreshing reports created by you and your team
taskmanager.automatically-table-remind = Reports automatically generated when a query takes a long time to complete
taskmanager.query-added-long = Query has been added to long running queries
taskmanager.empty-warning = There are no long running queries
taskmanager.recalculating = Recalculating




#task manager assistent notification strings
assistant.taskmanager.longTaskTooLong.title = This request is running for too long.
assistant.taskmanager.longTaskTooLong.message = We have switched it to report manager and will notify you when it is finished.
assistant.taskmanager.longTaskTooLong.info = Check its status in Utilities -> Report Manager
assistant.taskmanager.longTaskAlreadyRunning.title = A similar report is already running.
assistant.taskmanager.longTaskAlreadyRunning.message = Looks like report with same parameters already running in <a href='#/manage/tasks'>report manager</a>
assistant.taskmanager.completed.title = Report completed for {1}
assistant.taskmanager.completed.message = Results are ready for {0} reports. Check report manager to view them.
assistant.taskmanager.errored.title = Failed to generate report for {0}
assistant.taskmanager.errored.message = Some reports ({0}) couldn't be generated. Check report manager to try rerunning it.
assistant.taskmanager.errored.info = Under Utilities -> Task Manager

#placeholders
placeholder.old-password = Ancien mot de passe...
placeholder.new-password = Nouveau mot de passe...
placeholder.again = Encore...
placeholder.app-name = Entrez le nom de l'application...
placeholder.search-columns = Search Columns

placeholder.events.edit.description = Enter event description
placeholder.events.edit.count = Count
placeholder.events.edit.sum = Sum
placeholder.events.edit.duration = Duration

#application categories
application-category.books = Livres
application-category.business = Entreprise
application-category.education = Ãducation
application-category.entertainment = Divertissement
application-category.finance = Finance
application-category.games = Jeux
application-category.health-fitness = SantÃ© & Sport
application-category.lifestyle = Style de vie
application-category.medical = MÃ©dicale
application-category.music = Musique
application-category.navigation = Navigation
application-category.news = ActualitÃ©s
application-category.photography = Photographie
application-category.productivity = ProductivitÃ©
application-category.reference = RÃ©fÃ©rence
application-category.social-networking = RÃ©seaux sociaux
application-category.sports = Sports
application-category.travel = Voyage
application-category.utilities = Utilitaires
application-category.weather = MÃ©tÃ©o

#sidebar
sidebar.home = Home
sidebar.dashboard = Overview
sidebar.analytics = Statistiques
sidebar.analytics.users = User Analytics
web.sidebar.analytics.users = Visitor Analytics
sidebar.analytics.user-loyalty = User Loyalty
sidebar.analytics.session = Session Analytics
sidebar.analytics.sessions = Sessions
sidebar.analytics.countries = Pays
sidebar.analytics.devices = Terminaux
sidebar.analytics.app-versions = Versions de l'application
sidebar.analytics.versions = Versions
sidebar.analytics.carriers = OpÃ©rateurs
sidebar.analytics.platforms = Plateformes
sidebar.analytics.resolutions = RÃ©solutions
sidebar.analytics.technology = Technology
sidebar.analytics.technology-description = Overview details of your app or website traffic by your usersâ technology, such as platform, device, resolution, browsers and app version.
sidebar.analytics.geo = Geo
sidebar.engagement = Engagement
sidebar.events = ÃvÃ©nements
sidebar.events.all-events = All Events
sidebar.events.blueprint = Manage Events
sidebar.events.overview = Overview
sidebar.utilities = Utilities
sidebar.management = Gestion
sidebar.management.applications = Applications
sidebar.management.account = Mon Compte
sidebar.management.users = User Management
sidebar.management.longtasks = Report Manager
sidebar.management.help = Help Mode
sidebar.management.jobs = Jobs
sidebar.management.token-manager = Token Manager
sidebar.settings = Configurations
sidebar.logout = DÃ©connexion
sidebar.api_key = Api Key
sidebar.behavior = Behavior
sidebar.category.understand = Understand
sidebar.category.explore = Explore
sidebar.category.reach = Reach
sidebar.category.improve = Improve
sidebar.long-running-queries = Long running queries
sidebar.feedback = Feedback
sidebar.dashboard-tooltip = Dashboards
sidebar.main-menu = Main Menu
sidebar.my-profile = My Profile
sidebar.copy-api-key-success-message = Api Key has been copied to clipboard!

#dashboard
dashboard.apply = Appliquer
dashboard.home-desc = Overview of collected data
dashboard.empty-title = Nothing to show
dashboard.empty-text = There are no data to show. Enable features or Customize this page to see data about choosen features.
dashboard.audience = Audience
dashboard.customize-home = Customize Home
dashboard.avg-time-spent = Avg. Session Duration
dashboard.avg-time-spent-desc = The average amount of time spent per session on your application. It is calculated by dividing total duration spent across sessions by the total number of sessions.
dashboard.time-spent = Time Spent
dashboard.time-spent-desc = Total time spent for this period
dashboard.reqs-received = REQUÃTES REÃUES
dashboard.avg-reqs-received = Avg. Requests Received
dashboard.avg-reqs-received-desc = Number of write API requests Countly Server receives for each session (includes sessions, session extensions, events, etc)
dashboard.bounce-rate = BOUNCE RATE
dashboard.pages-per-visit = PAGES PER VISIT
dashboard.note-title-remaining = Remaining
dashboard.go-to-sessions = Go to Sessions
dashboard.total-sessions-desc = Total session data
#users
users.title = UTILISATEURS

#user-activity
user-activity.title = User Activity
web.user-activity.title = Visitor Activity
user-activity.description = Overview of the total number of users who started a session on your application, distributed in pre-set categories of numbers of sessions.
web.user-activity.description = Overview of the total number of visitors who started a session on your website, distributed in pre-set categories of numbers of sessions.
user-activity.barchart-all-users = All Users
web.user-activity.barchart-all-users = All Visitors
user-activity.barchart-thirty-days = Active Users (30 days)
web.user-activity.barchart-thirty-days = Active Visitors (30 days)
user-activity.barchart-seven-days = Active Users (7 days)
web.user-activity.barchart-seven-days = Active Visitors (7 days)
user-activity.table-session-count = Session Count (All Time)
user-activity.table-all-users = All Users
web.user-activity.table-all-users = All Visitors
user-activity.table-thirty-days = Active Users (30 days)
web.user-activity.table-thirty-days = Active Visitors (30 days)
user-activity.table-seven-days = Active Users (7 days)
web.user-activity.table-seven-days = Active Visitors (7 days)

#user-loyalty
user-loyalty.range.first-session = First Session
user-loyalty.range.hours = hours
user-loyalty.range.day = day
user-loyalty.range.days = days

#session-overview
session-overview.title = Session Overview
session-overview.description = Summary of all sessions your users have had in your application, in the selected time period.

#session-durations
session-durations.title = Session Durations
session-durations.description = Time period(s) for which users have opened your application.

#session-frequency
session-frequency.title = Session Frequency
session-frequency.description = Number of times users open your application, in the selected time period, distributed into frequency ranges.
session-frequency.table.frequency = Time since last session

#notes
notes.add-new-note = Add New Note
notes.edit-note = Edit Note
notes.note-details = Note Details
notes.note = Note
notes.type = Type
notes.owner = Owner
notes.enter-note = Enter your note
notes.visibility = Visibility
notes.date-and-time = Date and Time
notes.color = Color
notes.color-note-description = Select the indicator color of your note
notes.share-with = Share With
notes.shared-with = Shared With
notes.manage-notes = Manage Notes
notes.delete-note = Do you really want to delete note called {0}?
notes.all-notes = All Notes
notes.back-link = Back to Overview
notes.add-note = Add Note
notes.show-notes = Show Notes
notes.hide-notes = Hide Notes
notes.created-message = Note created successfully

#session-duration
session-duration.title = DURÃES SESSION
session-duration.table.duration = DurÃ©e de la session

#countries
countries.title = Countries
countries.description = An overview of the geographical distribution of your users and their sessions in the selected time period.
web.countries.description = An overview of the geographical distribution of your visitors and their sessions in the selected time period.
countries.table.country = Pays
countries.table.city = Ville
countries.back-to-list = Back to Country List
countries.back-to-map = Back to World Map
countries.google-api-key-remind = Google Maps API key must be setup to view city level data. Click here to setup Google Maps API key.
countries.go-to-countries = Go to Countries

#devices
devices.title = TERMINAL
devices.table.device = Terminal
devices.devices-and-types.title = Devices and Types
devices.go-to-technology = Go to Technology

#analytics users
user-analytics.overview-title = Users Overview
user-analytics.overview-desc = Overview of the main metrics and stats about your audience.
web.user-analytics.overview-title = Visitors Overview


#resolutions
resolutions.title = Resolutions
resolutions.description = Detailed information on the resolution settings of the devices through which your users access your application, in the selected time period.
web.resolutions.description = Detailed information on the resolution settings of the devices through which your visitors access your website, in the selected time period.

resolutions.table.resolution = RÃ©solution
resolutions.table.width = Largeur
resolutions.table.height = Hauteur


#device_type
device_type.title = Devices and Types
device_type.description =  Details of the device models and types from which your users access your application, in the selected time period.
web.device_type.description =  Details of the device models and types from which your visitors access your website, in the selected time period.
device_type.table.device_type = Device Type
device_type.types = Types
device_type.devices = Devices

#app-versions
app-versions.title = App Versions
app-versions.description = Detailed information on the application versions of your application accessed by your users, in the selected time period.
web.app-versions.description = Detailed information on the website versions of your website accessed by your visitors, in the selected time period.
app-versions.table.app-version = Version de l'App.

#carriers
carriers.title = Carriers
carriers.table.carrier = OpÃ©rateur
carriers.description = Detailed information on the network carriers of the devices through which your users access your application, in the selected time period.

#platforms
platforms.title = Platforms
platforms.pie-right = VERSIONS PLATEFORME
platforms.table.platform = Plateforme
platforms.table.platform-version = Version Plateforme
platforms.table.platform-version-for = Platform Versions for
platforms.description  = Details of the platforms on which yours users access your application, in the selected time period.
web.platforms.description  = Details of the platforms on which yours visitors access your website, in the selected time period.
platforms.platforms-for = Platforms for
platforms.version-distribution = Platforms version distribution
platforms.versions = Versions

#events
events.title = Events
events.blueprint-title = MANAGE EVENTS
events.blueprint-general = General
events.blueprint-events-tab-title = EVENTS
events.blueprint-event-group-title = MANAGE EVENT GROUPS
events.blueprint-event-group-new = New Event Group
events.blueprint-event-group-show.all = All Groups
events.blueprint-event-group-show.visible = Visible Groups
events.blueprint-event-group-show.hidden = Hidden Groups
events.blueprint-events-show.all = All Events
events.blueprint-events-show.hidden = Hidden Events
events.blueprint-events-show.visible = Visible Events
events.go-to-events = Go to Events

events.blueprint-events-properties-tooltip = Edited properties of this event will be updated on All Events and other plugins.
events.blueprint-event-groups-include-events-tooltip = Select at least 2 events to create an Event Group. New Event Groups will automatically  sum all the selected  event properties and report them.
events.blueprint-event-groups-properties-tooltip = Edited properties in this Event Group will be updated across All Events and other plugins.

events.blueprint-event-group-included-events = INCLUDED EVENTS
events.blueprint-eventgroups-tab-title = EVENT GROUPS
events.blueprint-edit = Edit
events.blueprint-drawer-title = Edit Event
events.blueprint-drawer-use-description = Use Description
events.general.description = Reorder, edit visibility, delete or change the appearance of a custom event
events.general.event = Event
events.general.event-description = Event description
events.general.action.perform-action = Perform action
events.general.action.delete = Delete
events.general.action.show = Show
events.general.action.hide = Hide
events.general.none-chosen = Select at least one event
events.general.update-not-successful = Updating events failed.
events.general.changes-saved = Changes saved
events.general.cancel = Cancel
events.general.confirm = Confirm
events.general.status = Status
events.general.status.visible = Visible
events.general.status.hidden = Hidden
events.general.show.title = Show
events.general.show.all = All
events.general.show.hidden = Hidden
events.general.show.visible = Visible
events.general.want-delete = You are about to delete multiple events ({0}). Do you want to continue?
events.general.want-delete-this = You are about to delete event called {0}. Do you want to continue?
events.general.want-delete-this-title = Delete an event?
events.general.want-delete-title = Delete events?
events.general.yes-delete-event =  Yes, delete event
events.general.yes-delete-events = Yes, delete events
events.general.want-to-discard = You have made changes to this event. Do you want to leave and discard those changes?
events.general.want-to-discard-title = Discard changes?
events.general.events-deleted = Selected events deleted sucessfully
events.general.no-hidden-events = There are no hidden events
events.general.no-visible-events = There are no visible events
events.back-to-events = Back to all events
events.general.error = Error
events.no-segmentation = Pas de segment
events.count = NOMBRE
events.sum = SOMME
events.dur = DURATION
events.table.count = Nombre
events.table.sum = Somme
events.table.avg-sum = Avg. Sum
events.table.dur = Duration
events.table.avg-dur = Avg. Duration
events.table.segmentation = Segmentation
events.edit.event-key = ClÃ© de l'Ã©vÃ©nement
events.edit.event-key-description = Event key as it is sent from the SDK
events.edit.event-name = Nom de l'Ã©vÃ©nement
events.edit.event-name-description = A display name for this event key to be used throughout the user interface
events.edit.event-description = Description
events.edit.event-description-description = A short description of this custom event and when it is triggered
events.edit.event-visibility = Event visibility
events.edit.event-visible = Event is visible
events.edit.event-invisible = Event is invisible
events.edit.event-visible-description = If an event is invisible server will continue to record data for it but it will not be displayed in the user interface.
events.edit.display-count = Display name for count
events.edit.display-count-description = A display name for the count property of this event
events.edit.display-sum = Display name for sum
events.edit.display-sum-description = A display name for the optional sum property of this event
events.edit.display-duration = Display name for duration
events.edit.display-duration-description = A display name for the optional duration property of this event.
events.edit.event-properties = Event properties
events.no-event = There are no events tracked for this application\!
events.delete-confirm = Vous Ãªtes sur le point de supprimer toutes les donnÃ©es associÃ©es Ã  l'Ã©vÃ©nement "{0}". Ãtes-vous sÃ»r de vouloir continuer ?
events.delete-confirm-many = You are about to delete all data associated with these events. Do you want to continue?
events.delete.multiple-events = {0} events
events.edit.omit-event-segments = Omit event segments
events.edit.omit-event-segments-description = Choose which segments of this custom event to omit. <b>Omitted segments will not be saved</b> in the future and past data for these segments will be purged immediately after you save these settings.
events.edit.omit-event-segments-description-drill = Data for these segments will still be stored in Drill.
events.edit.omit-event-segments-to-omit = Segments to omit
events.edit.omit-event-select-segments = Select segments
event.edit.omitt-warning = You are about to omit data for some segments of this event. Omitted segments will not be saved in the future and past data for these segments will be purged immediately after you save these settings. Do you want to continue?
events.overview.title = Overview
events.overview.drawer-title = Configure overview
events.overview.empty = You don't have any items in overview
events.overview.configure = Configure
events.overview.add-item=Add item
events.overview.added-items = Added items
events.overview.choose-event = Choose event
events.overview.choose-property = Choose property
events.overview.table.title-event = Event
events.overview.table.title-property = Property
events.overview.max-c = You can add maximum 12 previews in overview. Please delete some of previously added to add new.
events.overview.have-already-one = You have already one item with the same event and propery in overview.
events.overview.empty-title = Events overview is empty
events.overview.empty-text-admin = Configure events overview to visualise your most important custom events at a glance.
events.overview.empty-text-user = Request an admin of this application to configure events overview to visualise most important custom events at a glance.
events.overview.save-changes = Save changes
events.overview.unknown = NA
events.all.empty-title = This application doesn't have any custom events
events.all.empty-text = Log some custom events inside your application's code using the SDKs and visit this section later
events.top-events.title = Top Events By Count
events.top-events.24hours = 24-Hours
events.top-events.30days = 30-Days
events.top-events.yesterday = Yesterday
events.top-events.info-text = Updated {0} hrs ago
events.max-event-key-limit = Maximum limit of unique event keys ({0}) has been reached. Limit can be <a href="/dashboard#/manage/configurations/api">adjusted</a>.
events.max-segmentation-limit = Maximum limit of segmentations ({0}) in current event "{1}" has been reached. Limit can be <a href="/dashboard#/manage/configurations/api">adjusted</a>.
events.max-unique-value-limit = Maximum limit of unique values ({0}) in current event segmentation "{1}" has been reached. Limit can be <a href="/dashboard#/manage/configurations/api">adjusted</a>.

events.event-group-drawer-create = Create new Event Group
events.event-group-name = Event Group name
events.group-use-description = Use description
events.group-include-events = Include events
events.group-select-events = Select events to include
events.group-select-events-remind = Select events
events.group-visibility = Event Group Visibility
events.group-visibility-description = If an event group is invisible server will continue to record data for it but it will not be displayed in the user interface.
events.group-visibility-checkbox = Event Group is visible
events.group-invisibility-checkbox = Event Group is invisible
events.group-properties = Event Group Properties
events.event-group-count = Display name for count
events.event-group-count-description = A display name for the count property of this event
events.event-group-sum= Display name for sum
events.event-group-sum-description = A display name for the sum property of this event
events.event-group-duration = Display name for duration
events.event-group-duration-description = A display name for the duration property of this event
events.create-group = Create Event Group
events.save-group = Save Event Group
events.edit-your-group = Edit your Event Group

#export
export.export-as = Export as
export.export-number = Total of {0} items divided into {1} export file(s)
export.select = Select file to export
export.export = Export
export.columns-to-export = Columns to export
export.export-all-columns = Export all columns
export.documents = documents
export.export-columns-selected-count = {0}/{1} selected
export.format-if-possible = Format timestamps to readable date
export.format-if-possible-explain = Fields which are saved in data base as timestamps will be converted to show as date string like in table. For example: "Mon, 29 Jun 2020 17:14:15"
export.export-started = Export file is being generated. When ready you will see notification or can download it in report manager.
export.export-failed = Error upon attempting to export table data.
export.export-finished = Export completed.
export.export-finished-click = Click to download exported file.
export.file-name = File name

#management-applications
management-applications.title = Application Management
management-applications.my-new-app = Ma nouvelle application
management-applications.clear-data = Effacer les donnÃ©es
management-applications.clear-reset-data = Reset application
management-applications.clear-reset-explanation = Resets app to initial clean state like right after creation
management-applications.clear-all-data = Clear all data
management-applications.clear-all-explanation = Removes collected data but keeps configurations
management-applications.clear-1month-data = Clear data older than 1 month
management-applications.clear-3month-data = Clear data older than 3 months
management-applications.clear-6month-data = Clear data older than 6 months
management-applications.clear-1year-data = Clear data older than 1 year
management-applications.clear-2year-data = Clear data older than 2 years
management-applications.yes-clear-app = Yes, clear app data
management-applications.no-clear = No don't clear
management-applications.delete-an-app = Delete application
management-applications.yes-delete-app = Yes, delete the app
management-applications.application-name = Nom de l'Application
management-applications.application-name.tip = Entrez le nom de l'application
management-applications.application-details = Show details
management-applications.app-details = App Details
management-applications.application-no-details = Could not retrieve app details
management-applications.type = Application Type
management-applications.type.tip = Choose application type
management-applications.type.hint = All data will be recorded for this application type
management-applications.category = CatÃ©gorie
management-applications.category.tip = SÃ©lectionnez la catÃ©gorie
management-applications.app-id = ID application
management-applications.app-id.hint = This ID is used for the read API
management-applications.app-key = ClÃ© d'application
management-applications.app-key.hint = You'll need this key for SDK integration
management-applications.app-key.generate = Will generate automatically
management-applications.app-key-unique = This App Key is already in use
management-applications.time-zone = Fuseau Horaire
management-applications.time-zone.tip = SÃ©lectionnez un pays
management-applications.time-zone.hint = All data will be recorded in this timezone
management-applications.country.hint = City information will be only recorded for this country
management-applications.icon = App Icon
management-applications.add-application = Ajouter une application
management-applications.clear-confirm-all = You are about to clear all the collected data stored for your application. Do you want to continue?
management-applications.clear-confirm-period = You are about to clear all the collected data stored for your application within a selected period of time. Do you want to continue?
management-applications.clear-confirm-reset = You are about to reset your app to initial clean state. Do you want to continue?
management-applications.clear-admin = Seul les administrateurs peuvent effacer ces donnÃ©es.
management-applications.clear-success = Les donnÃ©es de votre application ont Ã©tÃ© supprimÃ©es avec succÃ¨s.
management-applications.reset-success = Application is successfully reset.
management-applications.delete-confirm = Vous Ãªtes sur le point de supprimer toutes les donnÃ©es associÃ©es Ã  votre application. Ãtes-vous sÃ»r de vouloir continuer ?
management-applications.delete-admin = Seul les administrateurs d'une application peuvent la supprimer.
management-applications.app-locked = Application is locked.
management-applications.icon-error = Seul les formats d'image jpg, png et gif sont acceptÃ©s
management-applications.no-app-warning = Avant de pouvoir collecter des donnÃ©es, vous devez ajouter une application Ã  votre compte.
management-applications.app-key-change-warning-title = Changing the App key
management-applications.app-key-change-warning = Changing the app key will cause all users from this point on to be recorded as new users even if they used your application before. <br/><br/>This action is only recommended if you are migrating an application from another server or changing the app key of a new application.
management-applications.app-key-change-warning-confirm = Continue, change the app key
management-applications.app-key-change-warning-EE = Changing the app key will cause all users from this point on to be recorded as new users even if they used your application before. <br/><br/>This action is only recommended if you are migrating an application from another server or changing the app key of a new application. <br/><br/>If your intention was to change the app key to stop collecting data for this application, recommended way of doing so is using Filtering Rules plugin.
management-applications.first-app-message2 = Great\! You can now embed Countly SDK into your application and start viewing your stats instantly. Don't forget to get your App Key from above.
management-applications.types.mobile = Mobile
management-applications.checksum-salt = Salt for checksum
management-applications.checksum-salt.hint = Will only accept requests where checksum is signed with the same salt in SDK
management-applications.app-domain = Website Domain
management-applications.app-creator = App created by
management-applications.app-created-at = Date of creation
management-applications.app-edited-at = Last edited
management-applications.app-last-data = Last data recorded (excluding Data Populator)
management-applications.app-users = Users with privileges
management-applications.global_admins = Global admins
management-applications.admins = Admins
management-applications.users = Users
management-applications.plugins = App settings
management-applications.plugins.save = Save
management-applications.plugins.smth = Something went wrong
management-applications.plugins.ok = OK, I understand
management-applications.plugins.save.nothing = No changes to save
management-applications.plugins.saved.nothing = No changes have been applied to this application
management-applications.plugins.saved.title = Plugin configuration
management-applications.plugins.saved = Changes saved successfully!
management-applications.plugins.error.server = Unknown server error
management-applications.create-first-app-title = Let's add your first application.
management-applications.create-first-app-description = After adding your first application you'll be ready to start collecting data.
management-applications.contact-an-admin = Please contact an administrator
management-applications.dont-access = You don't access rights to any application.
management-applications.plugins-description = Settings in this section will override global settings for the application
management-applications.application-lock-tooltip = Application lock prevents accidental data purge and data population
management-applications.application-tooltip-locked-text = App is locked. Unlock it to allow changes.
management-applications.application-tooltip-unlocked-text = App is unlocked. Lock it to restrict changes.

#management-users
management-users.read-permission-given-feature = Read permission granted automatically for
management-users.other-permissions-for = Other permissions for
management-users.removed-because-disabled = revoked automatically because of read permission disabled.
management-users.read-permission-all = Read permission automatically granted to all features.
management-users.other-permissions-removed = Other permissions revoked automatically because of read permission disabled.
management-users.permission-set-will-be-removed = Permission set will be removed
management-users.are-you-sure-to-continue = Are you sure to continue?
management-users.grant-user-access-to-apps = Grant User Access to Apps(s)
management-users.grant-user-access-to-apps-desc = Set user permissions in app and feature level
management-users.grant-admin-access-to-apps = Grant Admin Access to Apps(s)
management-users.grant-admin-access-description = User will have admin access to selected app(s)
management-users.add-permission-set = + Add Additional User Permission Set
management-users.profile-picture = Profile Picture
management-users.apps = Apps
management-users.sidebar-title = User Management
management-users.users = Users
management-users.view-title = Manage Users
management-users.editing-your-account = You\'re about to edit your own account. So you may be logged out automatically if changed something related with your permission. Do you want to contiue?
management-users.feature = Feature
management-users.create = Create
management-users.read = Read
management-users.update = Update
management-users.delete = Delete
management-users.edit-user = Edit settings
management-users.save-changes = Save Changes
management-users.create-new-user = Create new user
management-users.remove-permission-set = Remove permission set
management-users.create-user = Create user
management-users.discard-changes = Discard Changes
management-users.discard-confirm = You will lose all changes that you made. Are you sure to continue?
management-users.permission-settings = Permission settings
management-users.user-has-access-to = User has access to
management-users.role = RÃ´le
management-users.global-administrator = Global Administrator
management-users.full-name = Nom complet
management-users.enter-full-name = Enter Full Name
management-users.username = Nom d'utilisateur
management-users.enter-username = Enter Username
management-users.password = Mot de passe
management-users.generate-password = GÃ©nÃ©rer le mot de passe
management-users.enter-password = Enter Password
management-users.generate-password = GÃ©nÃ©rer le mot de passe
management-users.change-password = Changer le mot de passe
management-users.role = RÃ´le
management-users.drag-and-drop-or = Drag and drop or
management-users.browser = browser
management-users.files-to-add-picture = files to add picture
management-users.pp-size-warning = JPG, PNG and GIF files allowed. Maximum size is 5 MB.
management-users.remove-image = Remove image
management-users.email = E-mail
management-users.enter-email = Enter E-mail address
management-users.global-admin = Super Administrateur
management-users.lock-account = Lock Account
management-users.time-banned = Time Banned
management-users.remove-ban = Remove Ban
management-users.remove-ban-notify-title = Time ban removed.
management-users.remove-ban-notify-message = User time ban removed successfully.
management-users.admin = Administrateur
management-users.admin-of = Administrateur de
management-users.admin-of.tip = L'utilisateur n'est l'administrateur d'aucune application...
management-users.user = Utilisateur
management-users.user-of = Utilisateur de 
management-users.user-of.tip = L'utilisateur ne dispose pas des droits de visualisation sur une application...
management-users.no-role = Pas de rÃ´le
management-users.create-user = Create user
management-users.delete-user = Supprimer l'utilisateur
management-users.edit = Cliquez pour Ã©diter
management-users.all-roles = All roles
management-users.not-logged-in-yet = Not logged in yet
management-users.close = Cliquez pour fermer
management-users.password-change-confirm = You have changed {0}\''s password. Do you want a notification email to be sent?
management-users.delete-confirm = You are about to delete {0}\''s account. Do you want to continue?
management-users.delete-confirm-title = Delete user?
management-users.yes-delete-user  = Yes, delete user
management-users.email.invalid = Courriel invalide
management-users.email.exists = le courriel existe
management-users.username.exists = l'utilisateur existe
management-users.password.length = Password must be at least {0} characters long
management-users.password.has-char = Password must contain at least one uppercase letter
management-users.password.has-number = Password must contain at least one number
management-users.password.has-special = Password must contain at least one special character (?_*#!-$@,. etc)
management-users.add-user = Add user
management-users.revoke-access = Revoke Access
management-users.admin-user-alert = You need to assign the user as an admin or user at least to a single app
management-users.revoke-confirm = You are about to remove this user's access to your apps. Do you want to continue?
management-users.email-tip = User will be notified even if there is not an account for this email...
management-users.last_login = Last Login
management-users.created = Created
management-users.created-message = User created successfully\!
management-users.removed = Removed
management-users.removed-message = User removed successfully\!
management-users.remove-canceled = User remove canceled
management-users.updated = Updated
management-users.updated-message = User informations updated successfully\!
management-users.confirm-loss-checkboxes = You will lose all marked permissions below. Are you sure you want to continue?
management-users.select-app-first-title = Select app first
management-users.select-app-first-message = You need to select app or apps before you mark permission boxes
management-users.full-name-required = Full name area should be filled
management-users.username-required = Username area should be filled
management-users.email-required = E-mail area should be filled
management-users.email-invalid-format = Please check your email format
management-users.at-least-one-app-required = User should have permission for at least one app
management-users.view-user-logs = View user logs
management-users.this-will-delete-user = This will permanently delete the user. Continue?
management-users.warning = Warning
management-users.future-plugins = User will have this access type to all additional features that might be enabled in the future.
management-users.all-groups = All groups
management-users.group = Group
management-users.group-blank = -
management-users.reset-filters = Reset Filters
management-users.search-placeholder = Search in Features
management-users.reset-failed-logins = Reset failed logins
management-users.reset-failed-logins-success = Failed logins reset successfully\!
management-users.reset-failed-logins-failed = Failed to reset logins\!

#user-settings
user-settings.username = Nom d'utilisateur
user-settings.change-password = Changer le mot de passe
user-settings.old-password = Ancien mot de passe...
user-settings.new-password = Nouveau mot de passe...
user-settings.password-again = Encore...
user-settings.alert = Quelque chose est incorrect...
user-settings.success = Settings saved successfully\!
user-settings.api-key = ClÃ© API
user-settings.password-match = Les mots de passe ne correspondent pas
user-settings.old-password-match = Provide old password to change it
user-settings.old-password-not-match = Old password does not match
user-settings.password-not-old = New password must not be the same as old the one
user-settings.force-password-reset = It is time to change your password.
user-settings.please-correct-input = Please fix errors before submitting the form
user-settings.api-key-length = API key should be exactly 32 characters long
user-settings.api-key-restrict = API key should contain digits and alphabetical characters
user-settings.regenerate-api-key = Regenerate API key
user-settigs.delete-account = Delete account
user-settings.delete-account-title =  Delete account?
user-settings.delete-account-confirm = Do you really want to delete your account? You won't be able to recover it.
user-settings.password-mandatory = Password is mandatory!
user-settings.global admin limit = This account is last global admin account. Can't delete last global admin account.
user-settings.password not valid = Given password is not valid
user-settings.profile-picture = Profile Picture
user-settings.upload = Upload

#report manager
report-manager.name = Name
report-manager.desc = Description
report-manager.name-and-desc = Name and Description
report-manager.data = Data
report-manager.period = Period
report-manager.visibility = Visibility
report-maanger.manually-created = Manually Created
report-maanger.automatically-created = Automatically created
report-maanger.manually-created-title = Manually Created Reports
report-maanger.automatically-created-title = Automatically created Reports
report-manager.all-origins = All Origins
report-manager.all-types = All Types
report-manager.all-statuses = All Statuses
report-manager.all-owners = All Owners
report-manager.my-reports = My Reports
report-manager.all-sources = All Data Sources
report-manager.data-source = Data source
report-manager.select-data-source = Select data source
report-manager.runtime-type = Runtime type
report-manager.origin = Origin
report-manager.select-origin = Select origin
report-manager.owner = Owner
report-manager.status = Status
report-manager.app-independent = App Independent
report-manager.reset-filters = Reset filters
report-manager.filters = Filters

#app-users
app-users.download-debug-info = Download debug information
app-users.export-userdata = Export user's data
app-users.download-export = Download user's exported data
app-users.delete-export = Purge user's exported data
app-users.delete-userdata = Purge user's data completely
app-users.delete-userdata-confirm = Do you really want to purge all data associated with this user?
app-users.export-started = Exporting has started sucessfully.
app-users.export-finished = User data is exported. You can download it now.
app-users.export-finished-click = Click here to download.
app-users.export-failed = There was error during export process. There might be more information in logs.
app-users.export-deleted = Export data was purged
app-users.userdata-deleted  = User data was purged
app-users.yes-purge-data = Yes, purge data
app-users.no-dont-purge = No, don't purge
app-users.purge-confirm-title = Purge user's data completely?
app-users.debug-options = Debug options

#downloading-view
downloading-view.download-title = Your download should start soon
downloading-view.if-not-start = If download doesn't start automatically click here
downloading-view.download-not-available-title = Unable to start download
downloading-view.download-not-available-text = There was no export connected to this link. Export might be already deleted.

#token-manager
token_manager.page-title = Token Manager
token_manager.create-token = Create Token
token_manager.create-new-token = Create New Token
token_manager.table.id = Token ID
token_manager.table.ends =Valid until
token_manager.table.multi  =   Multiple times
token_manager.table.owner = Token owner
token_manager.table.app = App
token_manager.table.status  = Status
token_manager.table.endpoint = Endpoint
token_manager.table.endpoints = Endpoints
token_manager.table.endpoint-name = Endpoint Name
token_manager.table.endpoint-detail = ENDPOINT DETAILS
token_manager.table.endpoints-description = Given endpoints are interpreted as regularexpressions
token_manager.table.expiration-description = Set expiration time for token
token_manager.table.purpose = Description
token_manager.table.token-description = Token Description
token_manager.table.purpose-desc = Some information to help user identify created token.
token_manager.table.endpoint-desc = You can limit token to a single or multiple endpoints.<br/>Given endpoints are interpreted as regular expressions.
token_manager.table.multi-desc = Token can be used multiple times
token_manager.table.apps-title = Token Usage
token_manager.table.apps-limit = Allow token to be used only in some apps
token_manager.table.apps-allow = Allow token to be used in all apps
token_manager.table.limit-limit.label =  Limited Time
token_manager.table.limit-limit.text = Token will expire after the time you set
token_manager.table.limit-allow.label =  Unlimited Time
token_manager.table.limit-allow.text = Token can be used until it is deleted
token_manager.table.limit-title = Token Expiration
token_manager.table.enter-number = Enter number
token_manager.table.not-expire = No expiry
token_manager.table.all-apps = All apps
token_manager.limit.h = hours
token_manager.limit.d = days
token_manager.limit.m = months
token_manager.delete-token-confirm = Do you really want to delete this token?
token_manager.delete-token-confirm-title = Delete token?
token_manager.yes-delete-token = Yes, delete token
token_manager.delete-error = Deleting token failed
token_manager.select-apps-error = You have to chose apps or  set option to "Allow token to be used in all apps"
token_manager.select-expire-error = You have to choose after how long time token expires or set option to "Token can be used till it is deleted"
token_manager.table.delete-token = Delete token
token_manager.table.status-expired = Expired
token_manager.table.status-active = Active
token_manager.copy-token = Click to copy
token_manager.token-coppied = Token coppied
token_manager.query-param = Query Parameters
token_manager.query-param-value = Value
token_manager.query-param-desc = Limit by some parameter values(for example method=get_events) Value is used as regex when validating.
token_manager.add-new-endpoint = Add new endpoint
token_manager.add-param = Add parameter
token_manager.parameter  = Parameter
token_manager.select-apps  = Select Apps
token_manager.select-time-unit = Select time unit
token_manager.token-expiration-time = Expiration Time
token_manager.LoginAuthToken-description  =  This token is created when creating dashboard screenshots. <br/>If you are not currently rendering dashboard images, you can delete this token.
token_manager.LoggedInAuth-description = This token is used for keeping users session.<br/> Deleting it will log out user currently using it to keep session.


version_history.page-title = Countly version history
version_history.current-version = Current version
version_history.package-version = Package version
version_history.version = Version
version_history.upgraded = Upgraded / installed
version_history.alert-title = Version mismatch
version_history.alert-message = There is a version mismatch between version in files and in database. It may indicate that upgrade scripts didn't complete properly.
internal-events.[CLY]_session = Session

#jobs
jobs.back-to-jobs-list = Back to jobs list
jobs.job-name = Name
jobs.job-schedule = Schedule
jobs.job-next-run = Next Run
jobs.job-last-run = Last Run
jobs.job-status = Status
jobs.job-total-scheduled = Total
jobs.job-data = Data
jobs.run-duration = Duration
jobs.suspend = Suspend
jobs.suspend-successful-message = {0} suspended successfully
jobs.schedule = Schedule

systemlogs.action.task_manager_task_deleted = Report deleted
systemlogs.action.task_manager_task_updated = Report updated
systemlogs.action.task_manager_task_created = Report created

#events-overview
events.overview.title.new = Events Overview
events.overview.title.new.tooltip = Complete summary of all Events being tracked.
events.overview.total.event.count = Total number of Events triggered in the last 30 days.
events.overview.event.per.user = Average number of Events triggered per user, in the last 30 days.
events.overview.event.per.session = Average number of Events triggered per session, in the last 30 days.
events.overview.event.metrics = Overview of the metrics calculated by the identified Events, in the last 30 days.
events.overview.event.monitor.events = A quick summary of selected Events that you wish to monitor. To select Events that you want to highlight here, please click on 'Configure Events'.
events.overview.metrics = Event Metrics
events.overview.monitor = Monitor Events
events.overview.event = EVENT
events.overview.segment = SEGMENT NAME
events.overview.count = Count
events.overview.sum = Sum
events.overview.duration = Duration
events.overview.dur = Dur
events.overview.time.period = TIME PERIOD
events.percentage.of.total = % of Total
events.overview.top.events.by.count = Top Events by Count in the last 30 days
events.overview.updated = Updated
events.overview.total.events.count = Total Event Count
events.overview.events.per.session = Events Per Session
events.overview.events.per.user = Events Per User
events.overview.events.configure.events = Configure Events
events.overview.add.item = Add Item
events.overview.add.to.list = Add to List
events.overview.manage.items = Manage Items
events.overview.last-30days = Last 30 Days

#all-events
events.all.title.tooltip = Details of the stats of each Event, in the selected time period, and their comparison where applicable.
events.all.title.new = All Events
events.all.period = PERIOD
events.all.segmentation = SEGMENTATION BY
events.all.search.placeholder = Search in {0} Events
events.all.group = GROUP
events.all.error = Could not fetch data
events.all.count = Count
events.all.sum = Sum
events.all.duration = Duration
events.all.any.segmentation = Any segmentation
events.all.omitted.segments = OMITTED

#auto-refresh
auto-refresh.help= Automatically refresh can be adjusted through this switch
auto-refresh.enable= Enable Auto-refresh
auto-refresh.stop= Stop Auto-refresh
auto-refresh.is= Auto-refresh is
auto-refresh.enabled = Enabled

initial-setup.application = Application
initial-setup.add-first-application-title = Let's add your first application
initial-setup.add-first-application-byline = After adding your first application, you'll be ready to start collecting data
initial-setup.add-demo-application-title = Let's create a demo app for you!
initial-setup.application-type-label = Select your application type
initial-setup.time-zone-label = Select your time zone
initial-setup.application-sample-label = We'll populate some demo data for your app, which example sounds the best?
initial-setup.create-application = Create Application
initial-setup.continue-data-population = Continue with data population
initial-setup.consent-title = Before we start...
initial-setup.analytics-blurb-1 = We utilize Countly to understand user interactions and collect feedback, helping us enhance our product continuously. However, your privacy remains our priority.
initial-setup.analytics-blurb-2 = This analysis is done on the server level, so we won't see or collect any individual details or any data you record. The data is reported back only to our dedicated Countly server based in Europe. Please note, you can change your mind at any time in the settings.
initial-setup.analytics-question = Considering our commitment to maintaining your privacy and the potential benefits for product enhancement, would you be comfortable enabling Countly on this server?
initial-setup.analytics-no = No, maybe later
initial-setup.analytics-yes = Yes, enable Countly on this server
initial-setup.newsletter-blurb = We offer a newsletter brimming with recent updates about our product, news from Countly, and information on product analytics. We assure you - our aim is to provide value and insights, not clutter your inbox with unwanted emails.
initial-setup.newsletter-question = Would you be interested in subscribing to our newsletter?
initial-setup.newsletter-no = No, thank you.
initial-setup.newsletter-yes = Yes, subscribe me to the newsletter
initial-setup.quickstart-title = Quick Start Guide
