<!-- jobs.html -->
<div v-bind:class="[componentId]">
    <cly-header :title="i18n('sidebar.management.jobs')">
    </cly-header>

    <cly-main>
        <cly-section data-test-id="table-jobs">
            <!--
                cly-datatable-n uses :data-source="remoteTableDataSource"
                which is tied to our ServerDataTable in views.js.
                We define columns using el-table-column inside <template v-slot="scope">.
            -->
            <cly-datatable-n
                test-id="datatable-jobs"
                :data-source="remoteTableDataSource"
                v-on:row-click="goTo"
                :isClickable="true"
                :default-sort="{ prop: 'name', order: 'ascending' }"
                :persist-key="jobsTablePersistKey"
                class="jobs-table"
            >
                <template v-slot="scope">
                    <!-- Job Name Column -->
                    <el-table-column
                        prop="name"
                        :label="i18n('jobs.job-name')"
                        sortable="custom"
                        type="clickable"
                    >
                        <template v-slot="scope">
                            <span :data-test-id="'datatable-jobs-name-' + scope.$index">
                                {{ scope.row.name }}
                            </span>
                        </template>
                    </el-table-column>

                    <!-- Job Status Column -->
                    <el-table-column
                        prop="status"
                        :label="i18n('jobs.job-status')"
                        sortable="custom"
                    >
                        <template v-slot="scope">
                            <span :data-test-id="'datatable-jobs-status-' + scope.$index">
                                <cly-status-tag
                                    :text="scope.row.config.enabled ? scope.row.status : 'DISABLED'"
                                    :color="getColor(scope.row)"
                                ></cly-status-tag>

                                <!-- Show error tooltip if there's a failReason -->
                                <el-tooltip
                                    v-if="scope.row.failReason"
                                    :content="scope.row.failReason"
                                    placement="top"
                                >
                                    <i class="ion-alert-circled bu-ml-2" style="color: #D23F00;"></i>
                                </el-tooltip>
                            </span>
                        </template>
                    </el-table-column>

                    <!-- Job Schedule (Cron) Column -->
                    <el-table-column
                        prop="scheduleLabel"
                        :label="i18n('jobs.job-schedule')"
                        sortable="custom"
                    >
                        <template v-slot="scope">
                            <p :data-test-id="'datatable-jobs-schedule-' + scope.$index">
                                {{ scope.row.scheduleLabel }}
                            </p>
                            <p
                                :data-test-id="'datatable-jobs-schedule-detail-' + scope.$index"
                                class="bu-has-text-grey bu-is-size-7"
                            >
                                <template v-if="scope.row.scheduleOverridden">
                                    <el-tooltip
                                    :content= "'Default: ' + scope.row.schedule + (scope.row.configuredSchedule ? ' | Override: ' + scope.row.configuredSchedule : '')"
                                    placement="top">
                                        <i class="ion-alert-circled bu-ml-2""></i>
                                    <span class="bu-has-text-weight-bold">{{ i18n('jobs.override') }}:</span>
                                    </el-tooltip>
                                    {{scope.row.configuredSchedule}}
                                </template>
                                <template v-else>
                                    {{ scope.row.schedule }}
                                </template>
                            </p>
                        </template>
                    </el-table-column>

                    <!-- Next Run Column -->
                    <el-table-column
                        prop="nextRunAt"
                        :label="i18n('jobs.job-next-run')"
                        sortable="custom"
                    >
                        <template v-slot="scope">
                            <p :data-test-id="'datatable-jobs-next-run-date-' + scope.$index">
                                {{ scope.row.nextRunDate }}
                            </p>
                            <p
                                :data-test-id="'datatable-jobs-next-run-time-' + scope.$index"
                                class="bu-has-text-grey bu-is-size-7"
                            >
                                {{ scope.row.nextRunTime }}
                            </p>
                        </template>
                    </el-table-column>

                    <!-- Last Run Column -->
                    <el-table-column
                        prop="lastFinishedAt"
                        :label="i18n('jobs.job-last-run')"
                        sortable="custom"
                    >
                        <template v-slot="scope">
                            <p
                                v-html="scope.row.lastRun"
                                :data-test-id="'datatable-jobs-last-run-' + scope.$index"
                            ></p>
                        </template>
                    </el-table-column>

                    <!-- Total Column (if needed) -->
                    <el-table-column
                        prop="total"
                        :label="i18n('jobs.job-total-scheduled')"
                        sortable="custom"
                    >
                        <template v-slot="scope">
                            <p :data-test-id="'datatable-jobs-total-' + scope.$index">
                                {{ scope.row.total }}
                            </p>
                        </template>
                    </el-table-column>

                    <!-- Last Run Status Column -->
                    <el-table-column
                        prop="lastRunStatus"
                        :label="i18n('jobs.last-run-status')"
                        sortable="custom"
                    >
                        <template v-slot="scope">
                            <span :data-test-id="'datatable-jobs-last-run-status-' + scope.$index">
                                <cly-status-tag
                                    :text="scope.row.lastRunStatus"
                                    :color="getRunStatusColor(scope.row)"
                                >
                                </cly-status-tag>
                            </span>
                        </template>
                    </el-table-column>

                    <!-- Action Buttons Column -->
                    <el-table-column
                        align="center"
                        type="options"
                        header-align="center"
                        width="100"
                    >
                        <template v-slot="scope">

                            <cly-more-options
                                v-if="canSuspendJob"
                                @command="handleCommand($event, scope.row)"
                                placement="bottom-end"
                                :test-id="'datatable-jobs-' + scope.$index"
                            >
                                <el-dropdown-item
                                    command="enable"
                                    v-if="!scope.row.config.enabled"
                                >
                                    {{ i18n('jobs.enable') }}
                                </el-dropdown-item>
                                <el-dropdown-item
                                    command="disable"
                                    v-if="scope.row.config.enabled"
                                >
                                    {{ i18n('jobs.disable') }}
                                </el-dropdown-item>
                                <el-dropdown-item command="schedule">
                                    {{ i18n('jobs.change-schedule') }}
                                </el-dropdown-item>
                                <el-dropdown-item command="runNow">
                                    {{ i18n('jobs.run-now') }}
                                </el-dropdown-item>
                            </cly-more-options>
                        </template>
                    </el-table-column>
                </template>
            </cly-datatable-n>
            
            <!-- Schedule dialog moved outside of the table structure -->
            <el-dialog
                :title="i18n('jobs.schedule-configuration')"
                :visible.sync="scheduleDialogVisible"
                width="30%"
                append-to-body
            >
                <div class="bu-columns">
                    <div class="bu-column">
                        <div class="config-item">
                            <label>{{ i18n('jobs.schedule') }}:</label>
                            <el-input
                                v-model="selectedJobConfig.schedule"
                                :placeholder="selectedJobConfig.defaultSchedule"
                            ></el-input>

                            <div class="bu-mt-2 bu-is-size-7 bu-has-text-grey">
                                {{ i18n('jobs.schedule-hint') }}
                            </div>

                            <div
                                v-if="selectedJobConfig.scheduleLabel"
                                class="bu-mt-2 bu-is-size-7"
                            >
                                {{ selectedJobConfig.scheduleLabel }}
                            </div>
                        </div>
                    </div>
                </div>

                <span slot="footer" class="dialog-footer">
                    <el-button @click="scheduleDialogVisible = false">
                        {{ i18n('common.cancel') }}
                    </el-button>
                    <el-button
                        type="primary"
                        @click="saveSchedule"
                        :loading="saving"
                    >
                        {{ i18n('common.save') }}
                    </el-button>
                </span>
            </el-dialog>
        </cly-section>
    </cly-main>
</div>
