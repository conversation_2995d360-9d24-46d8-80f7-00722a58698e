<!-- jobs-details.html -->
<div v-bind:class="[componentId]">
    <cly-header :title="job_name">
        <template v-slot:header-top>
            <cly-back-link :title="i18n('jobs.back-to-jobs-list')"></cly-back-link>
        </template>
    </cly-header>

    <cly-main>
        <!-- Wait until jobDetails is loaded -->
        <template v-if="jobDetails">
            <!-- Job Configuration Header -->
            <h5 class="bu-is-size-5 bu-mb-4 bu-mt-5">
                {{ i18n('jobs.job-configuration') }}
            </h5>
            
            <!-- Job Overview Section -->
            <cly-section class="bu-pt-3">
                <div class="bu-columns">
                    <div class="bu-column">

                        <div class="bu-box bu-has-shadow-sm bu-p-5">
                            <!-- Base Configuration -->
                            <div class="bu-mb-4">
                                <h5 class="bu-is-size-5 bu-mb-3">
                                    {{ i18n('jobs.base-configuration') }}
                                </h5>
                                <div class="bu-field bu-mb-3 bu-is-flex">
                                    <label class="bu-label bu-mr-2 bu-is-flex-grow-0 bu-is-flex-shrink-0 bu-is-flex bu-is-align-items-center" style="width: 120px;">{{ i18n('jobs.status') }}:</label>
                                    <div class="bu-control bu-is-flex-grow-1">
                                        <cly-status-tag
                                            :text="jobDetails.config && jobDetails.config.enabled !== false
                                                ? (jobDetails.currentState && jobDetails.currentState.status ? jobDetails.currentState.status : i18n('common.unknown'))
                                                : i18n('jobs.disable').toUpperCase()"
                                            :color="getStatusColor(jobDetails)"
                                        >
                                        </cly-status-tag>
                                    </div>
                                </div>
                                <div class="bu-field bu-mb-3 bu-is-flex">
                                    <label class="bu-label bu-mr-2 bu-is-flex-grow-0 bu-is-flex-shrink-0 bu-is-flex bu-is-align-items-center" style="width: 120px;">{{ i18n('jobs.default-schedule') }}:</label>
                                    <div class="bu-control bu-is-flex-grow-1 bu-is-flex bu-is-align-items-center">
                                        <span class="bu-tag bu-is-info bu-is-light bu-mr-2">
                                            {{ jobDetails.config && jobDetails.config.defaultConfig && jobDetails.config.defaultConfig.schedule && jobDetails.config.defaultConfig.schedule.value
                                                ? jobDetails.config.defaultConfig.schedule.value
                                                : i18n('common.never') }}
                                        </span>
                                        <p class="bu-has-text-grey bu-is-italic" v-if="jobDetails.config && jobDetails.config.scheduleLabel">
                                            {{ jobDetails.config.scheduleLabel }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Configuration Overrides -->
                            <div
                                class="bu-mb-4"
                                v-if="hasOverrides"
                            >
                                <h5 class="bu-is-size-5 bu-mb-3">
                                    {{ i18n('jobs.active-overrides') }}
                                </h5>
                                <div
                                    class="bu-field bu-mb-3 bu-is-flex"
                                    v-if="jobDetails.config && jobDetails.config.scheduleOverride"
                                >
                                    <label class="bu-label bu-mr-2 bu-is-flex-grow-0 bu-is-flex-shrink-0 bu-is-flex bu-is-align-items-center" style="width: 120px;">{{ i18n('jobs.schedule-override') }}:</label>
                                    <div class="bu-control bu-is-flex-grow-1 bu-is-flex bu-is-align-items-center">
                                        <span class="bu-tag bu-is-warning bu-is-light bu-mr-2">
                                            {{ jobDetails.config.scheduleOverride }}
                                        </span>
                                        <p class="bu-has-text-grey bu-is-italic" v-if="jobDetails.config && jobDetails.config.scheduleOverrideLabel">
                                            {{ jobDetails.config.scheduleOverrideLabel }}
                                        </p>
                                    </div>
                                </div>
                                <div
                                    class="bu-field bu-mb-3"
                                    v-if="jobDetails.config && jobDetails.config.retryOverride"
                                >
                                    <div class="bu-is-flex">
                                        <label class="bu-label bu-mr-2 bu-is-flex-grow-0 bu-is-flex-shrink-0 bu-is-flex bu-is-align-items-center" style="width: 120px;">{{ i18n('jobs.retry-override') }}:</label>
                                        <div class="bu-control bu-is-flex-grow-1">
                                            <div class="bu-notification bu-is-light bu-is-info bu-p-3 bu-mt-2">
                                                <div class="bu-mb-2 bu-is-flex bu-is-align-items-center">
                                                    <strong class="bu-mr-2">{{ i18n('jobs.attempts') }}:</strong>
                                                    <span class="bu-tag bu-is-info bu-is-light">{{ jobDetails.config.retryOverride.attempts }}</span>
                                                </div>
                                                <div class="bu-is-flex bu-is-align-items-center">
                                                    <strong class="bu-mr-2">{{ i18n('jobs.delay') }}:</strong>
                                                    <span class="bu-tag bu-is-info bu-is-light">{{ jobDetails.config.retryOverride.delay }}ms</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Current State -->
                            <div class="bu-mb-4">
                                <h5 class="bu-is-size-5 bu-mb-3">
                                    {{ i18n('jobs.current-state') }}
                                </h5>
                                <div class="bu-field bu-mb-3 bu-is-flex">
                                    <label class="bu-label bu-mr-2 bu-is-flex-grow-0 bu-is-flex-shrink-0 bu-is-flex bu-is-align-items-center" style="width: 120px;">{{ i18n('jobs.last-run') }}:</label>
                                    <div class="bu-control bu-is-flex-grow-1">
                                        <span class="bu-tag bu-is-light">
                                            {{ jobDetails.currentState && jobDetails.currentState.lastRun ? formatDateTime(jobDetails.currentState.lastRun) : i18n('common.never') }}
                                        </span>
                                    </div>
                                </div>
                                <div class="bu-field bu-mb-3 bu-is-flex">
                                    <label class="bu-label bu-mr-2 bu-is-flex-grow-0 bu-is-flex-shrink-0 bu-is-flex bu-is-align-items-center" style="width: 120px;">{{ i18n('jobs.next-run') }}:</label>
                                    <div class="bu-control bu-is-flex-grow-1">
                                        <span class="bu-tag bu-is-light">
                                            {{ jobDetails.currentState && jobDetails.currentState.nextRun ? formatDateTime(jobDetails.currentState.nextRun) : i18n('common.not-scheduled') }}
                                        </span>
                                    </div>
                                </div>
                                <div class="bu-field bu-mb-3 bu-is-flex">
                                    <label class="bu-label bu-mr-2 bu-is-flex-grow-0 bu-is-flex-shrink-0 bu-is-flex bu-is-align-items-center" style="width: 120px;">{{ i18n('jobs.duration') }}:</label>
                                    <div class="bu-control bu-is-flex-grow-1">
                                        <span class="bu-tag bu-is-light">
                                            {{ jobDetails.currentState && jobDetails.currentState.lastRunDuration ? jobDetails.currentState.lastRunDuration + 's' : '-' }}
                                        </span>
                                    </div>
                                </div>
                                <div
                                    class="bu-field bu-mb-3"
                                    v-if="jobDetails.currentState && jobDetails.currentState.failReason"
                                >
                                    <div class="bu-is-flex">
                                        <label class="bu-label bu-mr-2 bu-is-flex-grow-0 bu-is-flex-shrink-0 bu-is-flex bu-is-align-items-center" style="width: 120px;">{{ i18n('jobs.last-failure') }}:</label>
                                        <div class="bu-control bu-is-flex-grow-1">
                                            <div class="bu-notification bu-is-light bu-is-danger bu-p-3 bu-mt-2">
                                                {{ jobDetails.currentState.failReason }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </cly-section>

            <h5 class="bu-is-size-5 bu-mb-4 bu-mt-5">
                {{ i18n('jobs.manual-job-history') }}
            </h5>
            
            <cly-section class="bu-pt-3">
                <div class="bu-box bu-has-shadow-sm bu-p-0">
                    <cly-datatable-n
                        :rows="jobRuns"
                        :columns="jobRunColumns"
                        :force-loading="isLoading"
                        class="jobs-history-table"
                    >
                        <template v-slot="scope">
                            <!-- Column: lastRunAt -->
                            <el-table-column
                                prop="lastRunAt"
                                :label="i18n('jobs.run-time')"
                                sortable="custom"
                                width="180"
                                header-align="left"
                            >
                                <template v-slot="scope">
                                    <span>
                                        {{ formatDateTime(scope.row.lastRunAt) }}
                                    </span>
                                </template>
                            </el-table-column>

                            <!-- Column: status -->
                            <el-table-column
                                prop="status"
                                :label="i18n('jobs.status')"
                                sortable="custom"
                                width="150"
                                header-align="left"
                            >
                                <template v-slot="scope">
                                    <div class="bu-is-flex bu-is-align-items-center">
                                        <cly-status-tag
                                            :text="scope.row.status"
                                            :color="getRunStatusColor(scope.row)"
                                        >
                                        </cly-status-tag>
                                        <el-tooltip
                                            v-if="scope.row.failReason"
                                            :content="scope.row.failReason"
                                            placement="top"
                                        >
                                            <i
                                                class="ion-alert-circled bu-ml-2"
                                                style="color: #d23f00"
                                            ></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- Column: duration -->
                            <el-table-column
                                prop="duration"
                                :label="i18n('jobs.duration')"
                                sortable="custom"
                                width="120"
                                header-align="left"
                            >
                                <template v-slot="scope">
                                    <span class="bu-tag bu-is-info bu-is-light" v-if="scope.row.duration">
                                        {{ scope.row.duration }}s
                                    </span>
                                    <span class="bu-has-text-grey" v-else>-</span>
                                </template>
                            </el-table-column>

                            <!-- Column: result -->
                            <el-table-column
                                prop="result"
                                :label="i18n('jobs.result')"
                                header-align="left"
                            >
                                <template v-slot="scope">
                                    <pre
                                        v-if="scope.row.dataAsString && scope.row.dataAsString !== '{}'"
                                        class="bu-p-3 bu-has-background-grey-lighter bu-is-family-code bu-has-radius bu-has-shadow-inner"
                                        style="max-height: 200px; overflow: auto;"
                                    >{{ scope.row.dataAsString }}</pre>
                                    <span class="bu-has-text-grey" v-else>{{ i18n('common.no-data') }}</span>
                                </template>
                            </el-table-column>
                        </template>
                    </cly-datatable-n>
                </div>
            </cly-section>
        </template>
    </cly-main>
</div>
