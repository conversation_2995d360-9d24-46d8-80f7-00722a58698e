<cly-section>
	<cly-datatable-n test-id="versions" :rows="appPlatformVersionRows" :resizable="true" :force-loading="isLoading">
		<template v-slot="scope">
			<el-table-column sortable="custom" prop="os_versions" :label="i18n('platforms.versions')">
				<template v-slot="rowScope">
					<div :data-test-id="'datatable-versions-versions-' + rowScope.$index">
						{{ rowScope.row.os_versions }}
					</div>
				</template>
			</el-table-column>
			<el-table-column sortable="custom" prop="t" :formatter="numberFormatter" :label="i18n('common.table.total-sessions')">
				<template v-slot="rowScope">
					<div :data-test-id="'datatable-versions-total-sessions-' + rowScope.$index">
						{{ rowScope.row.t }}
					</div>
				</template>
			</el-table-column>
			<el-table-column sortable="custom" prop="u" :formatter="numberFormatter" :label="i18n('common.table.total-users')">
				<template v-slot="rowScope">
					<div :data-test-id="'datatable-versions-total-users-' + rowScope.$index">
						{{ rowScope.row.u }}
					</div>
				</template>
			</el-table-column>
			<el-table-column sortable="custom" prop="n" :formatter="numberFormatter" :label="i18n('common.table.new-users')">
				<template v-slot="rowScope">
					<div :data-test-id="'datatable-versions-new-users-' + rowScope.$index">
						{{ rowScope.row.n }}
					</div>
				</template>
			</el-table-column>
		</template>
		<template v-slot:header-left>
			<el-select v-model="selectedPlatform" test-id="platforms">
				<el-option :key="item.value" :value="item.value" :label="item.name" v-for="item in choosePlatform"></el-option>
			</el-select>
		</template>
	</cly-datatable-n>
</cly-section>
