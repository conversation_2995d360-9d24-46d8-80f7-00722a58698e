<div v-bind:class="[componentId]">
    <div class="bu-level">
        <div class="bu-level-left">
            <h4 class="bu-pt-2 bu-mb-4 cly-vue-events-overview-subheadings--font" :data-test-id="`label-${i18n('events.overview.top.events.by.count').toLowerCase().replaceAll(/\s/g, '-')}`">{{i18n('events.overview.top.events.by.count')}} </h4>
        </div>
        <div class="bu-level-right">
            <a v-if="linkTo" class="cly-back-link text-medium" :data-test-id="`link-${linkTo.label.toLowerCase().replaceAll(/\s/g, '-')}`" :href="linkTo.href"><span :data-test-id="`link-text-${linkTo.label.toLowerCase().replaceAll(/\s/g, '-')}`">{{linkTo.label}}<i class="fas fa-arrow-right bu-pl-3" :data-test-id="`link-arrow-${linkTo.label.toLowerCase().replaceAll(/\s/g, '-')}`"></i></span></a>
        </div>
    </div>
    <cly-section>
        <!-- <vue-scroll :ops="scrollCards" ref="topSlider" @handle-scroll="handleCardsScroll"> -->
            <cly-metric-cards :multiline="false" v-loading="isLoading">
                <cly-metric-card formatting="long" :label="item.name" :test-id="'events-index-' + idx" :box-type="5" color="#097EFF" :tooltip="item.info" numberClasses="bu-is-flex bu-is-align-items-center" :key="idx" v-for="(item, idx) in items">
                    <template v-slot:number>{{item.value}}</template>
                    <span v-if="item.trend=='d'" slot="description" class="cly-trend-down" :data-test-id="'metric-card-events-index-' + idx + '-coloumn-trend-desc'"><i class="cly-trend-down-icon ion-android-arrow-down" :data-test-id="'metric-card-events-index-' + idx + '-coloumn-trend-arrow-icon'"></i>{{item.change}}</span>
                    <span v-else slot="description" class="cly-trend-up" :data-test-id="'metric-card-events-index-' + idx + '-coloumn-trend-desc'"><i class="cly-trend-up-icon ion-android-arrow-up" :data-test-id="'metric-card-events-index-' + idx + '-coloumn-trend-arrow-icon'"></i>{{item.change}}</span>
                </cly-metric-card>
                <div v-if="items.length <1 && !isLoading" class="technology-analytics-wrapper__empty-card" :data-test-id="`empty-card-${i18n('events.overview.top.events.by.count').toLowerCase().replaceAll(/\s/g, '-')}`">
                    <div class="text-medium" :data-test-id="`empty-card-label-${i18n('events.overview.top.events.by.count').toLowerCase().replaceAll(/\s/g, '-')}`">{{i18n('common.table.no-data')}}</div>
                </div>
            </cly-metric-cards>
        <!-- </vue-scroll> -->
    </cly-section>
</div>