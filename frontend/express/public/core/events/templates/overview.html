<script type="text/x-template" id="events-overview">
	<div v-bind:class="[componentId]">
		<cly-header
			:title="i18n('events.overview.title.new')"
			:tooltip="{description: i18n('events.overview.title.new.tooltip')}"
		>
		  <template v-slot:header-bottom>
			<i v-if="eventsOverview.length>0" class="far fa-clock cly-vue-events-header__subheading--widget" data-test-id="events-overview-updated-clock-icon"></i>
			<span v-if="eventsOverview.length>0" class="cly-vue-events-header__subheading--text" data-test-id="events-overview-updated-label">{{updatedAt}}</span>
		  </template>
		</cly-header>
		<cly-main>
		  <div class="bu-mb-4" v-if="eventsOverview.length>0">
			<el-button disabled icon="el-icon-date" data-test-id="events-overview-last-thirty-days-button">{{i18n('events.overview.last-30days')}}</el-button>
		  </div>
		  <cly-section v-if="eventsOverview.length>0">
			<cly-metric-cards :multiline="false" v-if="eventsOverview.length>0">
			  <cly-metric-card test-id="events-overview-total-event-count" formatting="long" :number=eventsOverview[0].value :tooltip="i18n('events.overview.total.event.count')" color="#097EFF" numberClasses="bu-is-flex bu-is-align-items-center">
					<span class="has-ellipsis" v-tooltip="eventsOverview[0].name">{{eventsOverview[0].name}}</span>
				<!-- <cly-tooltip-icon class="cly-vue-events-overview-top-events--tooltip" icon="ion-help-circled"></cly-tooltip-icon> -->
				<span v-if="eventsOverview[0].trend==='d'" slot="description" class="cly-vue-events-overview-top-events__description cly-trend-down" data-test-id="events-overview-total-event-count-desc"><i class="cly-trend-down-icon ion-android-arrow-down" data-test-id="events-overview-total-event-count-trend-icon"></i>{{eventsOverview[0].change}}</span>
				<span v-else slot="description" class="cly-vue-events-overview-top-events__description cly-trend-up" data-test-id="events-overview-total-event-count-desc"><i class="cly-trend-up-icon ion-android-arrow-up" data-test-id="events-overview-total-event-count-trend-icon"></i>{{eventsOverview[0].change}}</span>
			  </cly-metric-card>
			  <cly-metric-card test-id="events-overview-events-per-user" :number=eventsOverview[1].value color="#097EFF" :tooltip="i18n('events.overview.event.per.user')" numberClasses="bu-is-flex bu-is-align-items-center">
				<span class="has-ellipsis" v-tooltip="eventsOverview[1].name">{{eventsOverview[1].name}}</span>
				<!-- <cly-tooltip-icon class="cly-vue-events-overview-top-events--tooltip" icon="ion-help-circled"></cly-tooltip-icon> -->
				<div slot="number">{{eventsOverview[1].value}}</div>
				<span v-if="eventsOverview[1].trend==='d'" slot="description" class="cly-vue-events-overview-top-events__description cly-trend-down" data-test-id="events-overview-events-per-user-desc"><i class="cly-trend-down-icon ion-android-arrow-down" data-test-id="events-overview-events-per-user-trend-icon"></i>{{eventsOverview[1].change}}</span>
				<span v-else slot="description" class="cly-vue-events-overview-top-events__description cly-trend-up" data-test-id="events-overview-events-per-user-desc"><i class="cly-trend-up-icon ion-android-arrow-up" data-test-id="events-overview-events-per-user-trend-icon"></i>{{eventsOverview[1].change}}</span>
			  </cly-metric-card>
			  <cly-metric-card test-id="events-overview-events-per-session" :number=eventsOverview[2].value color="#097EFF" :tooltip="i18n('events.overview.event.per.session')" numberClasses="bu-is-flex bu-is-align-items-center">
				<span class="has-ellipsis" v-tooltip="eventsOverview[2].name">{{eventsOverview[2].name}}</span>
				<!-- <cly-tooltip-icon class="cly-vue-events-overview-top-events--tooltip" icon="ion-help-circled"></cly-tooltip-icon> -->
				<span v-if="eventsOverview[2].trend==='d'" slot="description" class="cly-vue-events-overview-top-events__description cly-trend-down" data-test-id="events-overview-events-per-session-desc"><i class="cly-trend-down-icon ion-android-arrow-down" data-test-id="events-overview-events-per-session-trend-icon"></i>{{eventsOverview[2].change}}</span>
				<span v-else slot="description" class="cly-vue-events-overview-top-events__description cly-trend-up" data-test-id="events-overview-events-per-session-desc"><i class="cly-trend-up-icon ion-android-arrow-up" data-test-id="events-overview-events-per-session-trend-icon"></i>{{eventsOverview[2].change}}</span>
			  </cly-metric-card>
			</cly-metric-cards>
		  </cly-section>
		  <h4 class="bu-pt-2 bu-mb-4 cly-vue-events-overview-subheadings--font" v-if="eventsOverview.length>0" data-test-id="events-overview-top-events-by-count">{{i18n('events.overview.top.events.by.count')}} </h4>
		  <div class="bu-columns bu-is-gapless bu-is-mobile bu-is-multiline white-bg">
			<events-breakdown-horizontal-tile 
			  :trend="item.trend"
			  :change="item.change"
			  :test-id= "'events-overview-top-event-' + idx"
			  :key="idx"
			  v-for="(item, idx) in topEvents">
			  <div class="bu-is-align-items-center bu-is-flex"  slot="title">
				  <span @click="onMetricClick(item)" v-tooltip="item.tooltip" class="text-medium has-ellipsis bu-is-clickable" :data-test-id="'events-overview-top-event-' + idx +  '-name-label'">{{item.name}}</span>
			  </div>
			  <div slot="countValue" class="cly-events-breakdown-horizontal-tile__value" :data-test-id="'events-overview-top-event-' + idx + '-value-label'">{{item.value}}</div>
			  <div class="bu-level-right" slot="totalPercentage">
				<div class="bu-level-item cly-events-breakdown-horizontal-tile__percentage" :data-test-id="'events-overview-top-event-' + idx + '-percentage-of-total-label'">
				  {{item.percentage}}{{i18n("events.percentage.of.total")}}
				</div>
			  </div>
			  <div slot="progressBar">
				<cly-progress-bar :percentage="item.percentage" :height=8 color="#017AFF" :test-id="'events-overview-top-event-' + idx"></cly-progress-bar>
			  </div>
			</events-breakdown-horizontal-tile>
		  </div>
		  <div class="bu-is-flex bu-mt-2 bu-mb-4">
			<h3 class="cly-vue-events-overview-subheadings" data-test-id="events-overview-subheading-event-metrics-label">{{i18n('events.overview.metrics')}} </h3>
			<cly-tooltip-icon :tooltip="i18n('events.overview.event.metrics')" class="cly-vue-events-overview-subheadings--tooltip" icon="ion-help-circled" data-test-id="events-overview-subheading-event-metrics-tooltip"></cly-tooltip-icon>
		  </div>
		  <cly-section>
			<detail-tables></detail-tables>
		  </cly-section>
		  <div class="bu-is-flex bu-mb-4">
			<h3 data-test-id="events-overview-subheading-monitor-events-label" class="cly-vue-events-overview-subheadings">{{i18n('events.overview.monitor')}} </h3>
			<cly-tooltip-icon :tooltip="i18n('events.overview.event.monitor.events')" class="cly-vue-events-overview-subheadings--tooltip" icon="ion-help-circled" data-test-id="events-overview-subheading-monitor-events-tooltip"></cly-tooltip-icon>
			<div class="cly-vue-events-overview-monitor-events__configure--position">
				<el-button @click="configureOverview" size="small" data-test-id="events-overview-configure-events-button">{{i18n('events.overview.events.configure.events')}}
				</el-button>  
			</div>
		  </div>
		  <cly-section class="cly-vue-events-overview-monitor-events" v-loading="isMonitorEventsLoading">
			<div>
			  <div class="bu-pl-4">
				<span class="cly-vue-events-overview-monitor-events--grey cly-vue-events-overview-monitor-events--text" data-test-id="events-overview-time-period-label">{{i18n('events.overview.time.period')}}</span>
				<cly-date-picker-g class="bu-m-2" test-id="events-overview-time-period" ></cly-date-picker-g>
			  </div>
			<div v-if="!isMonitorEventsLoading && monitorEventsData.length===0" class="cly-vue-events-overview-monitor-events__empty">
				<div class="cly-vue-events-overview-monitor-events__empty__block" >
								<cly-empty-view :hasAction="true" :actionFunc="configureOverview">
									<div slot="icon">
										<img data-test-id="events-overview-monitor-events-empty-logo" width="60" height="60" src="images/icons/empty-view-icon.svg"/>
									</div>
									<div slot="title">
										<div class="color-cool-gray-100 text-big font-weight-bold" data-test-id="events-overview-monitor-events-empty-title">{{i18n('events.all.empty-title')}}</div>
									</div>
									<div slot="subTitle">
										<div class="bu-mt-2 bu-mb-4 text-small color-cool-gray-50 bu-has-text-centered " data-test-id="events-overview-monitor-events-empty-subtitle">{{i18n('events.all.empty-text')}}</div>
									</div>
									<div slot="action">
										<el-button @click="configureOverview" type="text" data-test-id="events-overview-configure-events-link-button">{{i18n('events.overview.events.configure.events')}}</el-button>
									</div>
								</cly-empty-view>
				</div>
			</div>
			  <div v-else-if="!isMonitorEventsLoading && monitorEventsData.length" class="bu-columns bu-is-gapless bu-is-mobile bu-is-multiline" v-loading = "isMonitorEventsLoading"
>
				<monitor-events-breakdown-horizontal-tile
				  :trend="item.trend"
				  :change="item.change"
				  :test-id= "'events-overview-monitor-events-' + idx"
				  :key="idx"
				  v-for="(item, idx) in monitorEventsData">
				  <div class="bu-is-align-items-center bu-is-flex cly-monitor-events-breakdown-horizontal-tile__width-50" slot="title">
				  	<span v-tooltip="item.name" class="text-medium has-ellipsis cly-monitor-events-breakdown-horizontal-tile__overview-name" :data-test-id="'events-overview-monitor-events-event-' + idx + '-name-label'">{{decode(item.name)}}</span>
				  </div>
				  <div slot="countValue" class="cly-monitor-events-breakdown-horizontal-tile__overviewvalue" :data-test-id="'events-overview-monitor-events-event-' + idx + '-value-label'">{{item.total}}</div>
				  <div slot="propertyName" class="cly-monitor-events-breakdown-horizontal-tile__eventproperty" :data-test-id="'events-overview-monitor-events-event-' + idx + '-property-label'">{{item.eventProperty}}</div>
				  <div slot="barGraph" :data-test-id="'events-overview-monitor-events-event-' + idx + '-graph'">
					<cly-chart-bar :test-id="'events-overview-monitor-events-monitor-events-event-' + idx" :valFormatter="durCheck(item) ? valFormatter : undefined" xAxisLabelOverflow="unset" class="cly-vue-events-overview-monitor-events__charts" :noEmpty="true" :legend="monitorEventsLegend" :height=130 :option="item.barData" :autoresize=true :showZoom=false :showToggle=false :showDownload=false>
					</cly-chart-bar>
				  </div>
				</monitor-events-breakdown-horizontal-tile>
			  </div>
			  <div v-else style="height: 100px"></div>
			</div>
		  </cly-section>
		  <overview-drawer :controls="drawers.configureDrawer" :selectedEvents=selectedEvents ></overview-drawer>
		</cly-main>
	  </div>		
</script>
  <script type="text/x-template" id="overview-tables-events">
	<div>
		<cly-datatable-n test-id="events-overview" :default-sort="{prop: 'count', order: 'descending'}" :rows="eventsTableRows" :force-loading="isTableLoading" class="is-clickable">
		  <template v-slot:header-left="selectScope">
		  </template>
		  <template v-slot="scope">
			<el-table-column sortable="true" type="clickable" prop="name" :label="i18n('events.overview.event')" min-width="590px">
			  <template v-slot="rowScope">
				<div @click="onRowClick(rowScope.row)" :data-test-id="'datatable-event-metrics-event-' + rowScope.$index">
				  {{rowScope.row.name}}
				</div>
			  </template>
			</el-table-column>
			<el-table-column sortable="true" prop="count" :label="i18n('events.overview.count')" min-width="177px">
			  <template v-slot="rowScope">
				<div :data-test-id="'datatable-event-metrics-count-' + rowScope.$index">
				  {{formatNumber(rowScope.row.count)}}
				</div>
			  </template>
			</el-table-column>
			<el-table-column sortable="true" prop="sum" :label="i18n('events.overview.sum')" min-width="177px">
			  <template v-slot="rowScope">
				<div :data-test-id="'datatable-event-metrics-sum-' + rowScope.$index">
				  {{formatNumber(rowScope.row.sum)}}
				</div>
			  </template>
			</el-table-column>
			<el-table-column sortable="true" prop="duration" :label="i18n('events.overview.duration')" min-width="177px">
			  <template v-slot="rowScope">
				<div :data-test-id="'datatable-event-metrics-duration-' + rowScope.$index">
				  {{rowScope.row.duration>0?formatDurNumber(rowScope.row.duration):"-"}}
				</div>
			  </template>
			</el-table-column>
		  </template>
		</cly-datatable-n>
	  </div>
  </script>