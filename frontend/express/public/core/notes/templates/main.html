<div v-bind:class="[componentId]">
    <cly-header
        :title="i18n('notes.manage-notes')"
        :tooltip="{description: i18n('notes.manage-notes')}"
    >
        <template v-slot:header-top>
            <cly-back-link :title="i18n('common.back')"></cly-back-link>
        </template>
        <template v-if="hasCreateRight" v-slot:header-right>
            <div class="bu-level-item">
              <el-button type="success" @click="createNote" size="small" icon="el-icon-circle-plus">{{i18n('notes.add-new-note')}}
              </el-button>
            </div>
          </template>
    </cly-header>
    <cly-main>
        <cly-date-picker-g class="bu-mb-5"></cly-date-picker-g>
		<cly-confirm-dialog @cancel="closeDeleteForm" @confirm="submitDeleteForm" :before-close="closeDeleteForm" ref="deleteConfirmDialog" :visible.sync="showDeleteDialog" dialogType="danger" :saveButtonLabel="deleteDialogConfirmText" :cancelButtonLabel="i18n('common.no-dont-delete')" :title="deleteDialogTitle" >
			<template slot-scope="scope">
				{{deleteDialogText}}
			</template>
		</cly-confirm-dialog>
		<cly-datatable-n id="graph-notes-table" :rows="notes" :default-sort="{prop: 'indicator', order: 'ascending'}" :has-export="false">
            <template v-slot:header-left="selectScope">
                <el-select v-model="selectedType">
                    <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </template>
            <template v-slot="scope">
                <el-table-column min-width="25" :sortable="false" prop="indicator">
                </el-table-column>
                <el-table-column min-width="250" :sortable="false" prop="note" :label="i18n('notes.note')">
                    <template slot-scope="rowScope">
                        {{decodeHtml(rowScope.row.note)}}
                    </template>
                </el-table-column>
                <el-table-column sortable="custom" prop="noteType"  :label="i18n('notes.type')">
                </el-table-column>
                <el-table-column sortable="custom" prop="owner_name" :label="i18n('notes.owner')">
                </el-table-column>
                <el-table-column sortable="custom" prop="emails" :label="i18n('notes.shared-with')">
                        <template slot-scope="rowScope">
                        {{(rowScope.row.emails).toString() || '-'}}
                    </template>
                </el-table-column>
                <el-table-column sortable="custom"  :formatter="dateFormatter" prop="ts" :label="i18n('common.date')">
                </el-table-column>
                <el-table-column type="options">
                    <template v-slot="rowScope">
                    <cly-more-options v-if="rowScope.row.hover && (hasUpdateRight || hasDeleteRight)" size="small" @command="handleCommand($event, rowScope.row)">
                        <el-dropdown-item v-if="hasUpdateRight" command="edit">{{ i18n('common.edit') }}</el-dropdown-item>
                        <el-dropdown-item v-if="hasDeleteRight" command="delete">{{ i18n('common.delete') }}</el-dropdown-item>
                    </cly-more-options>
                    </template>
                </el-table-column>
            </template>
		</cly-datatable-n>
        <drawer :settings="drawerSettings" :controls="drawers.annotation" @cly-refresh="refresh"></drawer>
    </cly-main>
  </div>