#!/bin/bash

# OpenTelemetry Test Runner Script
# This script helps test the OpenTelemetry setup by running all components

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BOLD}${CYAN}=====================================${NC}"
    echo -e "${BOLD}${CYAN}  OpenTelemetry Test Suite${NC}"
    echo -e "${BOLD}${CYAN}=====================================${NC}"
    echo ""
}

print_step() {
    echo -e "${BOLD}${BLUE}➤ $1${NC}"
}

print_success() {
    echo -e "${BOLD}${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${BOLD}${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${BOLD}${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${CYAN}💡 $1${NC}"
}

# Check if we're in the right directory
if [[ ! -f "otel.js" || ! -f "otelexpress.js" ]]; then
    print_error "Please run this script from the observability directory containing otel.js and otelexpress.js"
    exit 1
fi

print_header

print_step "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi

NODE_VERSION=$(node --version)
print_success "Node.js version: $NODE_VERSION"

print_step "Making scripts executable..."
chmod +x dummy-otlp-server.js
chmod +x test-otel-setup.js
chmod +x test-otelexpress-setup.js
print_success "Scripts are now executable"

echo ""
print_info "Choose a test to run:"
echo "  1) Start dummy OTLP server (run this first!)"
echo "  2) Test otel.js setup"
echo "  3) Test otelexpress.js setup"  
echo "  4) Run all tests (requires multiple terminals)"
echo "  5) Help - show manual commands"
echo ""

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        print_step "Starting dummy OTLP server..."
        print_info "This will start a server on http://localhost:4318 to receive telemetry data"
        print_warning "Keep this running in a separate terminal while testing"
        echo ""
        node dummy-otlp-server.js
        ;;
    2)
        print_step "Testing otel.js setup..."
        print_warning "Make sure the dummy OTLP server is running on port 4318"
        sleep 2
        node test-otel-setup.js
        ;;
    3)
        print_step "Testing otelexpress.js setup..."
        print_warning "Make sure the dummy OTLP server is running on port 4318"
        sleep 2
        node test-otelexpress-setup.js
        ;;
    4)
        print_step "Running all tests..."
        print_warning "This requires multiple terminal windows!"
        echo ""
        print_info "Step 1: Open a new terminal and run:"
        echo "  cd $(pwd)"
        echo "  node dummy-otlp-server.js"
        echo ""
        print_info "Step 2: Open another terminal and run:"
        echo "  cd $(pwd)"
        echo "  node test-otel-setup.js"
        echo ""
        print_info "Step 3: Open another terminal and run:"
        echo "  cd $(pwd)"
        echo "  node test-otelexpress-setup.js"
        echo ""
        print_info "Press Enter when you're ready to continue or Ctrl+C to cancel..."
        read
        ;;
    5)
        print_info "Manual commands to run the tests:"
        echo ""
        echo "Terminal 1 - Start the dummy OTLP server:"
        echo "  cd $(pwd)"
        echo "  node dummy-otlp-server.js"
        echo ""
        echo "Terminal 2 - Test otel.js:"
        echo "  cd $(pwd)"
        echo "  node test-otel-setup.js"
        echo ""
        echo "Terminal 3 - Test otelexpress.js:"
        echo "  cd $(pwd)"
        echo "  node test-otelexpress-setup.js"
        echo ""
        print_info "The dummy OTLP server will show all received metrics and traces"
        print_info "Each test creates an HTTP server and makes automated requests"
        ;;
    *)
        print_error "Invalid choice. Please run the script again and choose 1-5."
        exit 1
        ;;
esac