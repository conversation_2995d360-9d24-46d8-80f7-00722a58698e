{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 6, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 20, "panels": [], "title": "Application Performance", "type": "row"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 1}, "id": 1, "options": {"legend": {"calcs": ["mean", "max", "p95"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Mean", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(countly_http_server_duration_milliseconds_sum{job=\"node\", http_route!~\"/metrics|/v1/traces\"}[$__rate_interval]) / rate(countly_http_server_duration_milliseconds_count{job=\"node\", http_route!~\"/metrics|/v1/traces\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "{{job}} - {{http_method}} {{http_route}}", "range": true, "refId": "A", "useBackend": false}], "title": "HTTP Server Request Duration", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 1}, "id": 5, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "countly_nodejs_eventloop_utilization_ratio{job=\"node\"} * 100", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "{{exported_job}}", "range": true, "refId": "A", "useBackend": false}], "title": "Event Loop Utilization %", "type": "gauge"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 21, "panels": [], "title": "System Resources", "type": "row"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 2, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "countly_system_memory_usage_bytes{state=\"used\", job=\"node\"}", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "{{job}} - Memory Used", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "code", "expr": "countly_system_memory_usage_bytes{state=\"used\", job=\"node\"}", "legendFormat": "{{job}} - <PERSON> Cached", "range": true, "refId": "B"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "code", "expr": "countly_system_memory_usage_bytes{state=\"used\", job=\"node\"}", "legendFormat": "{{job}} - Memory Free", "range": true, "refId": "C"}], "title": "System Memory Usage", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 13}, "id": 22, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "countly_system_cpu_load_average_1m", "legendFormat": "{{job}} - Load 1m", "refId": "A"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "countly_system_cpu_load_average_5m", "legendFormat": "{{job}} - Load 5m", "refId": "B"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "countly_system_cpu_load_average_15m", "legendFormat": "{{job}} - Load 15m", "refId": "C"}], "title": "CPU Load Average", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 23, "panels": [], "title": "Database & Event Loop", "type": "row"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 22}, "id": 4, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "countly_db_client_connections_usage{state=\"used\", job=\"node\"}", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "{{exported_job}} - DB Connections Used", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "code", "expr": "countly_db_client_connections_usage{state='idle', job=\"node\"}", "legendFormat": "{{exported_job}} - DB Connections Idle", "range": true, "refId": "B"}], "title": "DB Connections", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 22}, "id": 3, "options": {"legend": {"calcs": ["mean", "max", "p95"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "countly_nodejs_eventloop_delay_mean_seconds{job=\"node\"}", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "{{exported_job}} - Mean", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "code", "expr": "countly_nodejs_eventloop_delay_p90_seconds{job=\"node\"}", "legendFormat": "{{exported_job}} - P90", "range": true, "refId": "B"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "code", "expr": "countly_nodejs_eventloop_delay_p99_seconds{job=\"node\"}", "legendFormat": "{{exported_job}} - P99", "range": true, "refId": "C"}], "title": "Event Loop Delay", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 30}, "id": 24, "panels": [], "title": "V8 Runtime & Network", "type": "row"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 31}, "id": 6, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "rate(countly_v8js_gc_duration_seconds_sum{job=\"node\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "{{exported_job}} - {{v8js_gc_type}}", "range": true, "refId": "A", "useBackend": false}], "title": "Garbage Collection Duration", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 31}, "id": 25, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(countly_system_network_io_bytes_total{direction=\"receive\", job=\"node\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "{{job}} - By<PERSON> Received", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(countly_system_network_io_bytes_total{direction=\"transmit\", job=\"node\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "{{job}} - Bytes Transmitted", "range": true, "refId": "B", "useBackend": false}], "title": "Network I/O", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 39}, "id": 26, "panels": [], "title": "HTTP Client Performance", "type": "row"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}, "id": 27, "options": {"legend": {"calcs": ["mean", "max", "p95"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "sum(rate(countly_http_client_duration_milliseconds_sum{job=\"node\"}[$__rate_interval])) / sum(rate(countly_http_client_duration_milliseconds_count{job=\"node\"}[$__rate_interval]))", "legendFormat": "{{job}} - Avg Duration", "refId": "A"}], "title": "HTTP Client Avg Request Duration", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "id": 28, "options": {"legend": {"calcs": ["p95"], "displayMode": "table", "placement": "bottom"}}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "histogram_quantile(0.95, sum(rate(countly_http_client_duration_milliseconds_bucket{job=\"node\"}[$__rate_interval])) by (le))", "legendFormat": "{{job}} - p95 Latency", "refId": "A"}], "title": "HTTP Client p95 Request Duration", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 48}, "id": 29, "panels": [], "title": "Logging Metrics", "type": "row"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 49}, "id": 30, "options": {}, "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "sum(rate(countly_log_duration_seconds_sum{job=\"node\"}[$__rate_interval])) / sum(rate(countly_log_duration_seconds_count{job=\"node\"}[$__rate_interval]))", "legendFormat": "{{job}} - Log Duration Avg", "refId": "A"}], "title": "Log Operation Duration", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "req/s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 49}, "id": 31, "options": {}, "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "rate(countly_log_entries_total{job=\"node\", level=\"error\"}[$__rate_interval])", "legendFormat": "{{job}} - <PERSON><PERSON>r <PERSON>gs <PERSON>", "refId": "A"}], "title": "Error Log Rate", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 57}, "id": 32, "panels": [], "title": "System Disk & Filesystem Metrics", "type": "row"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "bytes/s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 58}, "id": 33, "options": {}, "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "rate(countly_system_disk_io_bytes_total{job=\"node\"}[$__rate_interval])", "legendFormat": "{{job}} - Disk I/O", "refId": "A"}], "title": "Disk I/O Throughput", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 58}, "id": 34, "options": {}, "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "countly_system_filesystem_usage_bytes{job=\"node\"}", "legendFormat": "{{job}} - Filesystem Usage", "refId": "A"}], "title": "Filesystem Usage", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 66}, "id": 35, "options": {}, "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "countly_system_disk_pending_operations{job=\"node\"}", "legendFormat": "{{job}} - Disk Pending Ops", "refId": "A"}], "title": "Disk Pending Operations", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 74}, "id": 36, "panels": [], "title": "V8 Memory Metrics", "type": "row"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 75}, "id": 37, "options": {}, "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "countly_v8js_memory_heap_used_bytes{job=\"node\"}", "legendFormat": "{{job}} - He<PERSON> Used", "refId": "A"}], "title": "V8 Heap Memory Used", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 75}, "id": 38, "options": {}, "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "expr": "countly_v8js_memory_heap_limit_bytes{job=\"node\"}", "legendFormat": "{{job}} - Heap Limit", "refId": "A"}], "title": "V8 Heap Memory Limit", "type": "timeseries"}], "preload": false, "refresh": "10s", "schemaVersion": 40, "tags": ["nodejs"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Node.js Metrics2", "uid": "nodejs2", "version": 1, "weekStart": ""}