receivers:
  otlp:
    protocols:
      grpc:
        endpoint: "0.0.0.0:4317"
      http:
        endpoint: "0.0.0.0:4318"

  hostmetrics:
    collection_interval: 30s
    scrapers:
      cpu:
      memory:
      load:
      disk:
      filesystem:
      network:

processors:
  batch:
    timeout: 1s
    send_batch_size: 1024

  # Filter out internal metrics
  filter:
    metrics:
      exclude:
        match_type: regexp
        metric_names:
          - '^process_.*'
          - '^go_.*'
          - '^promhttp_.*'
          - '^runtime_.*'

  resource:
    attributes:
      # Application identification
      - key: application
        value: countly
        action: insert

      # Service identification
      - key: service.name
        from_attribute: service_name
        action: upsert
      - key: service.instance.id
        from_attribute: service.instance.id
        action: upsert
      - key: service.namespace
        value: "countly"
        action: insert

      # Component categorization
      - key: component.type
        value: "service"
        action: insert

      # Clean up unnecessary labels
      - key: service_type
        action: delete

  memory_limiter:
    check_interval: 1s
    limit_mib: 1024
    spike_limit_mib: 256

exporters:
  prometheus:
    endpoint: "0.0.0.0:8889"
    namespace: "countly"
    const_labels:
      environment: "development"
    send_timestamps: true
    metric_expiration: 180m
    enable_open_metrics: true

  otlp:
    endpoint: "tempo:4317"
    tls:
      insecure: true
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
      max_elapsed_time: 300s

extensions:
  health_check:
    endpoint: "0.0.0.0:13133"
    path: "/health"
    check_collector_pipeline:
      enabled: true
      interval: "5s"
      exporter_failure_threshold: 5

service:
  extensions: [health_check]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch, resource]
      exporters: [otlp]

    metrics:
      receivers: [otlp, hostmetrics]
      processors: [memory_limiter, filter, resource, batch]
      exporters: [prometheus]

  telemetry:
    logs:
      level: "info"
