image:
  name: us-docker.pkg.dev/countly-01/internal/countly-ee-core:bitbucket-master
  username: _json_key
  password: '$ART_REG_KEY'
  run-as-user: 0

definitions:
  services: 
    mongo: 
      image: mongo
    docker:
      memory: 3072
  steps:
    - step: &ESLint
        name: ESLint
        script:
          - apt-get update -y
          - apt-get -y install shellcheck
          - bash scripts/shellcheck.sh
          - npx eslint .
          - cp -rf plugins/* /opt/countly/plugins
          - cp -rf /opt/countly/plugins/plugins.ee.json /opt/countly/plugins/plugins.json
          - cd /opt/countly
          - countly task dist-all
    - step: &ESLint-master
        name: ESLint-master
        image:
          name: us-docker.pkg.dev/countly-01/internal/countly-ee-core:bitbucket-master
          username: _json_key
          password: '$ART_REG_KEY'
          run-as-user: 0
        script:
          - false || [[ $BITBUCKET_PR_DESTINATION_BRANCH = "master" ]] || exit 0
          - apt-get update -y
          - apt-get -y install shellcheck
          - bash scripts/shellcheck.sh
          - npx eslint .
          - cp -rf plugins/* /opt/countly/plugins
          - cp -rf /opt/countly/plugins/plugins.ee.json /opt/countly/plugins/plugins.json
          - cd /opt/countly
          - countly task dist-all
    - step: &ESLint-next
        name: ESLint-next
        image:
          name: us-docker.pkg.dev/countly-01/internal/countly-ee-core:bitbucket-next
          username: _json_key
          password: '$ART_REG_KEY'
          run-as-user: 0
        script:
          - false || [[ $BITBUCKET_PR_DESTINATION_BRANCH = "next" ]] || exit 0
          - apt-get update -y
          - apt-get -y install shellcheck
          - bash scripts/shellcheck.sh
          - npx eslint .
          - cp -rf plugins/* /opt/countly/plugins
          - cp -rf /opt/countly/plugins/plugins.ee.json /opt/countly/plugins/plugins.json
          - cd /opt/countly
          - countly task dist-all
    - step: &Tests
        name: Tests
        script:
          - cp -rf plugins/* /opt/countly/plugins
          - bash scripts/countly.prepare.tests.sh
          - node /opt/countly/bin/scripts/install_plugins.js
          - cd /opt/countly
          - "sed -i 's/port: 3001,/port: 3001, workers: 1,/' api/config.js"
          - /sbin/my_init &
          - sleep 100
          - npm install
          - npx grunt mochaTest
        services:
          - mongo
    - step: &Tests-master
        name: Tests-master
        image:
          name: us-docker.pkg.dev/countly-01/internal/countly-ee-core:bitbucket-master
          username: _json_key
          password: '$ART_REG_KEY'
          run-as-user: 0
        script:
          - false || [[ $BITBUCKET_PR_DESTINATION_BRANCH = "master" ]] || exit 0
          - cp -rf plugins/* /opt/countly/plugins
          - bash scripts/countly.prepare.tests.sh
          - node /opt/countly/bin/scripts/install_plugins.js
          - cd /opt/countly
          - "sed -i 's/port: 3001,/port: 3001, workers: 1,/' api/config.js"
          - /sbin/my_init &
          - sleep 100
          - npm install
          - npx grunt mochaTest
        services:
          - mongo
    - step: &Tests-next
        name: Tests-next
        image:
          name: us-docker.pkg.dev/countly-01/internal/countly-ee-core:bitbucket-next
          username: _json_key
          password: '$ART_REG_KEY'
          run-as-user: 0
        script:
          - false || [[ $BITBUCKET_PR_DESTINATION_BRANCH = "next" ]] || exit 0
          - cp -rf plugins/* /opt/countly/plugins
          - bash scripts/countly.prepare.tests.sh
          - node /opt/countly/bin/scripts/install_plugins.js
          - cd /opt/countly
          - "sed -i 's/port: 3001,/port: 3001, workers: 1,/' api/config.js"
          - /sbin/my_init &
          - sleep 100
          - npm install
          - npx grunt mochaTest
        services:
          - mongo
    - step: &FullTests
        name: FullTests
        script:
          - cp -rf plugins/* /opt/countly/plugins
          - bash scripts/countly.prepare.full.tests.sh
          - node /opt/countly/bin/scripts/install_plugins.js
          - cd /opt/countly
          - "sed -i 's/port: 3001,/port: 3001, workers: 1,/' api/config.js"
          - /sbin/my_init &
          - sleep 100
          - npm install
          - npx grunt mochaTest
        services:
          - mongo
    - step: &ChokeTest
        name: ChokeTest
        script:
          - cp -rf plugins/* /opt/countly/plugins
          - bash /opt/countly/bin/backup/run.sh
          - rm -f /opt/countly/plugins/plugins.json
          - mv plugins/plugins.ee.json /opt/countly/plugins/plugins.json
          - node /opt/countly/bin/scripts/install_plugins.js
          - COUNTLY_SETTINGS=${COUNTLY_SETTINGS:-"{\"api.batch_processing\":true,\"api.batch_read_processing\":true,\"drill.record_meta\":false,\"drill.batch_inserts\":true}"}
          - echo $COUNTLY_SETTINGS
          - echo "db.plugins.update({_id:\"plugins\"},{\$set:$COUNTLY_SETTINGS},{upsert:true})"
          - mongo localhost/countly --eval "db.plugins.update({_id:\"plugins\"},{\$set:$COUNTLY_SETTINGS},{upsert:true})"
          - "sed -i 's/port: 3001,/port: 3001, workers: 1,/' /opt/countly/api/config.js"
          - /sbin/my_init &
          - sleep 100
          - countly config list values
          - cp scripts/choke_test.js /opt/countly
          - nodejs /opt/countly/choke_test.js
        services:
          - mongo
    - step: &Deploy
        name: Deploy
        script:
        - pipe: atlassian/ssh-run:0.2.3
          variables:
            SSH_USER: 'countly'
            SERVER: '$BITBUCKET_BRANCH.count.ly'
            COMMAND: 'bash /home/<USER>/deploy.sh > /home/<USER>/logs/countly-deploy-bitbucket.log'
    - step: &DockerCore
          name: DockerCore
          size: 2x
          script:
            - echo $ART_REG_KEY | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
            - apt-get update && apt-get install git -y
            - source scripts/modify.version.sh
            - bash scripts/prepare.ee.package.sh
            - tar xzf "../countly-enterprise-edition-v${Version}.tar.gz"
            - cd countly
            - docker build -f Dockerfile-core --build-arg GEOIP=$GEOIP --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps -t "us-docker.pkg.dev/countly-01/gcr.io/countly-ee-core:${Version}" -t "us-docker.pkg.dev/countly-01/gcr.io/countly-ee-core:latest" .
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/countly-ee-core:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/countly-ee-core:latest"
          services:
            - mongo
            - docker
    - step: &Docker
          name: Docker
          size: 2x
          script:
            - echo $ART_REG_KEY | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
            - apt-get update && apt-get install git -y
            - source scripts/modify.version.sh
            - bash scripts/prepare.ee.package.sh
            - tar xzf "../countly-enterprise-edition-v${Version}.tar.gz"
            - cd countly
            - docker build -f Dockerfile-api --build-arg GEOIP=$GEOIP --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps -t "us-docker.pkg.dev/countly-01/gcr.io/api:${Version}" -t "us-docker.pkg.dev/countly-01/gcr.io/api:latest" .
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/api:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/api:latest"
            - docker build -f Dockerfile-frontend --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps -t "us-docker.pkg.dev/countly-01/gcr.io/frontend:${Version}" -t "us-docker.pkg.dev/countly-01/gcr.io/frontend:latest" .
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/frontend:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/frontend:latest"
          services:
            - mongo
            - docker
    - step: &DockerCentos
          name: DockerCentos
          size: 2x
          script:
            - echo $ART_REG_KEY | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
            - apt-get update && apt-get install git -y
            - source scripts/modify.version.sh
            - bash scripts/prepare.ee.package.sh
            - tar xzf "../countly-enterprise-edition-v${Version}.tar.gz"
            - cd countly
            - docker build -f Dockerfile-centos-api --build-arg GEOIP=$GEOIP --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps -t "us-docker.pkg.dev/countly-01/gcr.io/centos-api:${Version}" -t "us-docker.pkg.dev/countly-01/gcr.io/centos-api:latest" .
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/centos-api:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/centos-api:latest"
            - docker build -f Dockerfile-centos-frontend --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps -t "us-docker.pkg.dev/countly-01/gcr.io/centos-frontend:${Version}" -t "us-docker.pkg.dev/countly-01/gcr.io/centos-frontend:latest" .
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/centos-frontend:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/gcr.io/centos-frontend:latest"
          services:
            - mongo
            - docker
    - step: &DockerLDAP
          name: DockerLDAP
          size: 2x
          script:
            - echo $ART_REG_KEY | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
            - apt-get update && apt-get install git -y
            - source scripts/modify.version.sh
            - bash scripts/prepare.ldap.package.sh
            - tar xzf "../countly-enterprise-edition-v${Version}_ldap.tar.gz"
            - cd countly
            - docker build -f Dockerfile-api --build-arg GEOIP=$GEOIP --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,ldap -t "us-docker.pkg.dev/countly-01/auth-plugins/ldap-api:${Version}" -t "us-docker.pkg.dev/countly-01/auth-plugins/ldap-api:latest" .
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/ldap-api:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/ldap-api:latest"
            - docker build -f Dockerfile-frontend --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,ldap -t "us-docker.pkg.dev/countly-01/auth-plugins/ldap-frontend:${Version}" -t "us-docker.pkg.dev/countly-01/auth-plugins/ldap-frontend:latest" .
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/ldap-frontend:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/ldap-frontend:latest"
          services:
            - mongo
            - docker
    - step: &DockerOKTA
          name: DockerOKTA
          size: 2x
          script:
            - echo $ART_REG_KEY | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
            - apt-get update && apt-get install git -y
            - source scripts/modify.version.sh
            - bash scripts/prepare.okta.package.sh
            - tar xzf "../countly-enterprise-edition-v${Version}_okta.tar.gz"
            - cd countly
            - docker build -f Dockerfile-api --build-arg GEOIP=$GEOIP --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,okta -t "us-docker.pkg.dev/countly-01/auth-plugins/okta-api:${Version}" -t "us-docker.pkg.dev/countly-01/auth-plugins/okta-api:latest" .
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/okta-api:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/okta-api:latest"
            - docker build -f Dockerfile-frontend --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,okta -t "us-docker.pkg.dev/countly-01/auth-plugins/okta-frontend:${Version}" -t "us-docker.pkg.dev/countly-01/auth-plugins/okta-frontend:latest" .
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/okta-frontend:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/okta-frontend:latest"
          services:
            - mongo
            - docker
    - step: &DockerCognito
          name: DockerCognito
          size: 2x
          script:
            - echo $ART_REG_KEY | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
            - apt-get update && apt-get install git -y
            - source scripts/modify.version.sh
            - bash scripts/prepare.cognito.package.sh
            - tar xzf "../countly-enterprise-edition-v${Version}_cognito.tar.gz"
            - cd countly
            - docker build -f Dockerfile-api --build-arg GEOIP=$GEOIP --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,attribution,cognito -t "us-docker.pkg.dev/countly-01/auth-plugins/cognito-api:${Version}" -t "us-docker.pkg.dev/countly-01/auth-plugins/cognito-api:latest" .
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/cognito-api:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/cognito-api:latest"
            - docker build -f Dockerfile-frontend --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,attribution,cognito -t "us-docker.pkg.dev/countly-01/auth-plugins/cognito-frontend:${Version}" -t "us-docker.pkg.dev/countly-01/auth-plugins/cognito-frontend:latest" .
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/cognito-frontend:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/cognito-frontend:latest"
          services:
            - mongo
            - docker
    - step: &DockerAD
          name: DockerAD
          size: 2x
          script:
            - echo $ART_REG_KEY | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
            - apt-get update && apt-get install git -y
            - source scripts/modify.version.sh
            - bash scripts/prepare.ad.package.sh
            - tar xzf "../countly-enterprise-edition-v${Version}_ad.tar.gz"
            - cd countly
            - docker build -f Dockerfile-api --build-arg GEOIP=$GEOIP --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,active_directory -t "us-docker.pkg.dev/countly-01/auth-plugins/ad-api:${Version}" -t "us-docker.pkg.dev/countly-01/auth-plugins/ad-api:latest" .
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/ad-api:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/ad-api:latest"
            - docker build -f Dockerfile-frontend --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,active_directory -t "us-docker.pkg.dev/countly-01/auth-plugins/ad-frontend:${Version}" -t "us-docker.pkg.dev/countly-01/auth-plugins/ad-frontend:latest" .
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/ad-frontend:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/auth-plugins/ad-frontend:latest"
          services:
            - mongo
            - docker
    - step: &DockerGateway
          name: DockerGateway
          size: 2x
          script:
            - echo $ART_REG_KEY | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
            - apt-get update && apt-get install git -y
            - source scripts/modify.version.sh
            - bash scripts/prepare.gateway.package.sh
            - tar xzf "../countly-enterprise-edition-v${Version}_gateway.tar.gz"
            - cd countly
            - docker build -f Dockerfile-api --build-arg GEOIP=$GEOIP --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,gateway_monitor,gateway_processor -t "us-docker.pkg.dev/countly-01/gateway/gateway-api:${Version}" -t "us-docker.pkg.dev/countly-01/gateway/gateway-api:latest" .
            - docker push "us-docker.pkg.dev/countly-01/gateway/gateway-api:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/gateway/gateway-api:latest"
            - docker build -f Dockerfile-frontend --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps,gateway_monitor,gateway_processor -t "us-docker.pkg.dev/countly-01/gateway/gateway-frontend:${Version}" -t "us-docker.pkg.dev/countly-01/gateway/gateway-frontend:latest" .
            - docker push "us-docker.pkg.dev/countly-01/gateway/gateway-frontend:${Version}"
            - docker push "us-docker.pkg.dev/countly-01/gateway/gateway-frontend:latest"
          services:
            - mongo
            - docker
    - step: &DockerBitbucket
          name: DockerBitbucket
          size: 2x
          script:
            - echo $ART_REG_KEY | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
            - apt-get update && apt-get install git -y
            - "sed -i 's/.*version:.*/    version: \"bitbucket\",/' version.info.js"
            - bash scripts/prepare.ee.package.sh
            - tar xzf "../countly-enterprise-edition-vbitbucket.tar.gz"
            - cd countly
            - docker build -f Dockerfile-core --build-arg COUNTLY_PLUGINS=mobile,web,desktop,plugins,density,locale,browser,sources,views,drill,funnels,retention_segments,flows,cohorts,surveys,remote-config,ab-testing,formulas,activity-map,concurrent_users,revenue,logger,systemlogs,populator,reports,crashes,push,geo,block,users,star-rating,slipping-away-users,compare,server-stats,dbviewer,crash_symbolication,crashes-jira,groups,white-labeling,alerts,times-of-day,compliance-hub,onboarding,active_users,performance-monitoring,config-transfer,consolidate,data-manager,hooks,dashboards,heatmaps -t "us-docker.pkg.dev/countly-01/internal/countly-ee-core:bitbucket-${BITBUCKET_BRANCH//\//-}" .
            - docker push "us-docker.pkg.dev/countly-01/internal/countly-ee-core:bitbucket-${BITBUCKET_BRANCH//\//-}"
          services:
            - mongo
            - docker
    - step: &Package
          name: Package
          #image: centos/nodejs-10-centos7
          script:
            - apt-get update && apt-get install git -y
            - echo "${Type:=ee}" #set default Type value
            - echo "${Upload:=bitbucket}" # set default Upload value
            - source scripts/modify.version.sh
            - bash scripts/prepare.${Type}.package.sh
            - bash scripts/upload.package.${Upload}.sh
    - step: &AllPackages
          name: AllPackages
          script:
            - apt-get update && apt-get install git -y
            - source scripts/modify.version.sh
            - bash scripts/prepare.all.package.sh
            - bash scripts/upload.package.google.sh
    - step: &TagGithub
          name: TagGithub
          script:
            - apt-get update && apt-get install git -y
            - cd ..
            - git clone -b master "https://github.com/Countly/countly-server"
            - cd countly-server
            - git tag "$BITBUCKET_TAG" master
            - git push "https://${GH_REPO_TOKEN}@github.com/Countly/countly-server" --tags

pipelines:
  branches:
    master:
      - step: *Deploy
    next:
      - step: *Deploy

  pull-requests:
    '**': 
      - parallel:
        - step: *ESLint-master
        - step: *Tests-master
        - step: *ESLint-next
        - step: *Tests-next
  custom:
    eslint:
      - step: *ESLint
    tests:
      - step: *Tests
    package:
      - variables:
        - name: Version
        - name: Type
        - name: Upload
      - step: *ESLint
      - step: *Package
    
    all-packages:
      - variables:
        - name: Version
      - step: *ESLint
      - step: *AllPackages

    docker-bitbucket:
      - step: *DockerBitbucket

    docker-core:
      - variables:
        - name: Version
      - step: *DockerCore
      
    docker:
      - variables:
        - name: Version
      - step: *Docker
      
    docker-ldap:
      - variables:
        - name: Version
      - step: *DockerLDAP
      
    docker-okta:
      - variables:
        - name: Version
      - step: *DockerOKTA
      
    docker-cognito:
      - variables:
        - name: Version
      - step: *DockerCognito
      
    docker-ad:
      - variables:
        - name: Version
      - step: *DockerAD
      
    docker-gateway:
      - variables:
        - name: Version
      - step: *DockerGateway

    docker-centos:
      - variables:
        - name: Version
      - step: *DockerCentos
      
    docker-all:
      - variables:
        - name: Version
      - step: *ESLint
      - parallel:
        - step: *Docker
        - step: *DockerCore
        - step: *DockerCentos
        - step: *DockerLDAP
        - step: *DockerOKTA
        - step: *DockerCognito
        - step: *DockerAD
        - step: *DockerGateway
    
    deploy:
      - step: *Deploy
      
    full-test:
      - step: *FullTests
      
    choke-test:
      - variables:
        - name: COUNTLY_SETTINGS
      - step: *ChokeTest

  tags:
    '**': 
      - step: *ESLint
      - parallel:
        - step: *Docker
        - step: *DockerCore
        - step: *DockerCentos
        - step: *DockerLDAP
        - step: *DockerOKTA
        - step: *DockerCognito
        - step: *DockerAD
        - step: *DockerGateway
        - step: *Package
        - step: *AllPackages
        - step: *TagGithub