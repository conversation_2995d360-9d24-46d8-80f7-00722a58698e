# Frontend Dependencies in Countly

This document lists the frontend dependencies used in the Countly project, specifically in the core/frontend directory.

## JavaScript Frameworks & Libraries

| Software | Type | License | URL | Location |
|----------|------|---------|-----|----------|
| Express | Web Framework | MIT | https://expressjs.com/ | core/package.json |
| Vue.js | JavaScript Framework | MIT | https://vuejs.org/ | core/frontend/express/public/javascripts/utils/vue/vue.min.js |
| Vuex | State Management for Vue.js | MIT | https://vuex.vuejs.org/ | core/frontend/express/public/javascripts/utils/vue/vuex.min.js |
| Vue Composition API | Composition API for Vue.js | MIT | https://github.com/vuejs/composition-api | core/frontend/express/public/javascripts/utils/vue/composition-api.min.js |
| Element UI | UI Framework for Vue.js | MIT | https://element.eleme.io/ | core/frontend/express/public/javascripts/utils/vue/element-ui.js |
| jQuery | DOM Manipulation Library | MIT | https://jquery.com/ | core/frontend/express/public/javascripts/dom/jquery/jquery.js |
| jQuery UI | UI Components for jQuery | MIT | https://jqueryui.com/ | core/frontend/express/public/javascripts/dom/jqueryui/jquery-ui.js |
| Backbone.js | MVC Framework | MIT | https://backbonejs.org/ | core/frontend/express/public/javascripts/utils/backbone-min.js |
| Handlebars.js | Templating Engine | MIT | https://handlebarsjs.com/ | core/frontend/express/public/javascripts/utils/handlebars.js |
| Underscore.js | JavaScript Utility Library | MIT | https://underscorejs.org/ | core/frontend/express/public/javascripts/utils/underscore-min.js |
| Moment.js | Date Manipulation Library | MIT | https://momentjs.com/ | core/frontend/express/public/javascripts/utils/moment/ |
| Moment Timezone | Timezone Library | MIT | https://momentjs.com/timezone/ | core/package.json |
| Bluebird | Promise Library | MIT | http://bluebirdjs.com/ | core/package.json |
| Argon2 | Hashing Library | MIT | https://github.com/ranisalt/node-argon2 | core/package.json |
| EJS | Templating Engine | Apache-2.0 | https://ejs.co/ | core/package.json |
| JSON Editor | JSON Editing Library | MIT | https://github.com/josdejong/jsoneditor | core/frontend/express/public/javascripts/utils/jsoneditor/ |
| Leaflet | Interactive Maps Library | BSD-2-Clause | https://leafletjs.com/ | core/frontend/express/public/javascripts/utils/leaflet.js |
| Vue2 Leaflet | Vue wrapper for Leaflet | MIT | https://vue2-leaflet.netlify.app/ | core/frontend/express/public/javascripts/utils/vue/vue2-leaflet.min.js |
| Selectize | Enhanced Select Box | Apache-2.0 | https://selectize.dev/ | core/frontend/express/public/javascripts/utils/selectize.min.js |
| GridStack | Draggable, Resizable Grid Layouts | MIT | https://gridstackjs.com/ | core/frontend/express/public/javascripts/dom/gridstack/gridstack-h5.js |
| DataTables | Interactive Tables | MIT | https://datatables.net/ | core/frontend/express/public/javascripts/dom/dataTables/js/jquery.dataTables.js |
| Dropzone.js | File Upload Library | MIT | https://www.dropzonejs.com/ | core/frontend/express/public/javascripts/utils/dropzone.js |
| Vue2 Dropzone | Vue wrapper for Dropzone.js | MIT | https://github.com/rowanwins/vue-dropzone | core/frontend/express/public/javascripts/utils/vue/vue2Dropzone.min.js |
| Tether | Positioning Engine | MIT | https://github.com/shipshapecode/tether | core/frontend/express/public/javascripts/dom/drop/tether.min.js |
| Drop.js | Dropdown Library | MIT | https://github.com/HubSpot/drop | core/frontend/express/public/javascripts/dom/drop/drop.min.js |
| Pace.js | Page Load Progress Bar | MIT | https://github.com/CodeByZach/pace | core/frontend/express/public/javascripts/dom/pace/pace.min.js |
| Tipsy | Tooltip Library | MIT | https://github.com/jaz303/tipsy | core/frontend/express/public/javascripts/dom/tipsy/jquery.tipsy.js |
| Sortable | Drag-and-Drop Library | MIT | https://github.com/SortableJS/Sortable | core/frontend/express/public/javascripts/utils/Sortable.min.js |
| Vuedraggable | Vue wrapper for Sortable | MIT | https://github.com/SortableJS/Vue.Draggable | core/frontend/express/public/javascripts/utils/vue/vuedraggable.umd.min.js |
| Vee-Validate | Form Validation for Vue.js | MIT | https://vee-validate.logaretm.com/ | core/frontend/express/public/javascripts/utils/vue/vee-validate.full.min.js |
| Vue Tooltip | Tooltip Library for Vue.js | MIT | https://github.com/Akryum/v-tooltip | core/frontend/express/public/javascripts/utils/vue/v-tooltip.min.js |
| Vue Clipboard | Clipboard for Vue.js | MIT | https://github.com/Inndy/vue-clipboard2 | core/frontend/express/public/javascripts/utils/vue/vue-clipboard.min.js |
| Vue Color | Color Picker for Vue.js | MIT | https://github.com/xiaokaike/vue-color | core/frontend/express/public/javascripts/utils/vue/vue-color.min.js |
| Vue JSON Pretty | JSON Viewer for Vue.js | MIT | https://github.com/leezng/vue-json-pretty | core/frontend/express/public/javascripts/utils/vue/vue-json-pretty.min.js |
| Vuescroll | Scrollbar for Vue.js | MIT | https://vuescrolljs.org/ | core/frontend/express/public/javascripts/utils/vue/vuescroll.min.js |
| Vue Good Table | Data Table for Vue.js | MIT | https://xaksis.github.io/vue-good-table/ | core/frontend/express/public/javascripts/utils/vue/vue-good-table.min.js |
| Element Tiptap | Rich Text Editor for Vue.js | MIT | https://github.com/Leecason/element-tiptap | core/frontend/express/public/javascripts/utils/element-tiptap.umd.min.js |

## Visualization Libraries

| Software | Type | License | URL | Location |
|----------|------|---------|-----|----------|
| D3.js | Data Visualization | BSD-3-Clause | https://d3js.org/ | core/frontend/express/public/javascripts/visualization/d3/d3.min.js |
| Flot | Charting Library | MIT | https://www.flotcharts.org/ | core/frontend/express/public/javascripts/visualization/flot/jquery.flot.js |
| Rickshaw | Time Series Charting | MIT | https://github.com/shutterstock/rickshaw | core/frontend/express/public/javascripts/visualization/rickshaw/rickshaw.min.js |
| ECharts | Charting Library | Apache-2.0 | https://echarts.apache.org/ | core/frontend/express/public/javascripts/utils/echarts.5.min.js |
| Vue ECharts | Vue wrapper for ECharts | MIT | https://github.com/ecomfe/vue-echarts | core/frontend/express/public/javascripts/utils/vue/vue-echarts.umd.min.js |
| Gauge.js | Gauge Charts | MIT | https://github.com/bernii/gauge.js | core/frontend/express/public/javascripts/visualization/gauge.min.js |
| Peity | Mini Charts | MIT | https://github.com/benpickles/peity | core/frontend/express/public/javascripts/visualization/jquery.peity.min.js |
| jQuery Sparkline | Mini Charts | BSD | https://github.com/gwatts/jquery.sparkline | core/frontend/express/public/javascripts/visualization/jquery.sparkline.js |

## CSS Frameworks & Libraries

| Software | Type | License | URL | Location |
|----------|------|---------|-----|----------|
| Bulma | CSS Framework | MIT | https://bulma.io/ | core/frontend/express/public/stylesheets/bulma/ |
| Font Awesome | Icon Library | CC BY 4.0, SIL OFL 1.1, MIT | https://fontawesome.com/ | core/frontend/express/public/stylesheets/font-awesome/ |
| Ionicons | Icon Library | MIT | https://ionicons.com/ | core/frontend/express/public/stylesheets/ionicons/ |
| Material Design | Design System | Apache-2.0 | https://material.io/ | core/frontend/express/public/stylesheets/material/ |

## jQuery Plugins

| Software | Type | License | URL | Location |
|----------|------|---------|-----|----------|
| jQuery Easing | Animation Effects | BSD | https://github.com/gdsmith/jquery.easing | core/frontend/express/public/javascripts/dom/jquery.easing.1.3.js |
| jQuery Form | Form Handling | MIT | https://github.com/jquery-form/form | core/frontend/express/public/javascripts/dom/jquery.form.js |
| jQuery Noisy | Noise Texture Generator | MIT | https://github.com/DanielRapp/Noisy | core/frontend/express/public/javascripts/dom/jquery.noisy.min.js |
| jQuery Sticky Headers | Sticky Headers | MIT | https://github.com/jmosbech/StickyHeaders | core/frontend/express/public/javascripts/dom/jquery.sticky.headers.js |
| jQuery SlimScroll | Scrollbar | MIT | https://github.com/rochal/jQuery-slimScroll | core/frontend/express/public/javascripts/dom/slimScroll.min.js |
| jQuery hoverIntent | Hover Intent | MIT | https://github.com/briancherne/jquery-hoverIntent | core/frontend/express/public/javascripts/utils/jquery.hoverIntent.minified.js |
| jQuery i18n | Internationalization | MIT | https://github.com/jquery-i18n-properties/jquery-i18n-properties | core/frontend/express/public/javascripts/utils/jquery.i18n.properties.js |
| jQuery Idle Timer | Idle Detection | MIT | https://github.com/thorst/jquery-idletimer | core/frontend/express/public/javascripts/utils/jquery.idle-timer.js |
| jQuery Title Alert | Title Notifications | MIT | https://github.com/heyman/jquery-titlealert | core/frontend/express/public/javascripts/utils/jquery.titlealert.js |
| jQuery Validate | Form Validation | MIT | https://github.com/jquery-validation/jquery-validation | core/frontend/express/public/javascripts/utils/jquery.validate.js |
| jQuery XSS | XSS Protection | MIT | https://github.com/leizongmin/js-xss | core/frontend/express/public/javascripts/utils/jquery.xss.js |
| jQuery Amaran | Notifications | MIT | https://github.com/hakanersu/AmaranJS | core/frontend/express/public/javascripts/utils/jquery.amaran.min.js |

## Node.js Middleware & Utilities

| Software | Type | License | URL | Location |
|----------|------|---------|-----|----------|
| Connect-Mongo | Session Store | MIT | https://github.com/jdesboeufs/connect-mongo | core/frontend/express/libs/connect-mongo.js |
| Express-Expose | Expose Variables to Client | MIT | https://github.com/tj/express-expose | core/frontend/express/libs/express-expose.js |
| Connect-Flash | Flash Messages | MIT | https://github.com/jaredhanson/connect-flash | core/package.json |
| Cookie-Parser | Cookie Parsing Middleware | MIT | https://github.com/expressjs/cookie-parser | core/package.json |
| Formidable | Form Data Parsing | MIT | https://github.com/node-formidable/formidable | core/package.json |
| Express-Session | Session Middleware | MIT | https://github.com/expressjs/session | core/package.json |
| Method-Override | HTTP Method Override | MIT | https://github.com/expressjs/method-override | core/package.json |
| CSRF | CSRF Protection | MIT | https://github.com/expressjs/csurf | core/package.json |
| Errorhandler | Error Handling | MIT | https://github.com/expressjs/errorhandler | core/package.json |
| Basic-Auth | HTTP Basic Authentication | MIT | https://github.com/jshttp/basic-auth | core/package.json |
| Body-Parser | Request Body Parsing | MIT | https://github.com/expressjs/body-parser | core/package.json |
| Express-Rate-Limit | Rate Limiting Middleware | MIT | https://github.com/nfriedly/express-rate-limit | core/package.json |
| Jimp | Image Processing | MIT | https://github.com/oliver-moran/jimp | core/package.json |

## Development & Build Tools

| Software | Type | License | URL | Location |
|----------|------|---------|-----|----------|
| Grunt | Task Runner | MIT | https://gruntjs.com/ | core/package.json |
| ESLint | Code Linting | MIT | https://eslint.org/ | core/package.json |
| Sass | CSS Preprocessor | MIT | https://sass-lang.com/ | core/package.json |
| Mocha | Testing Framework | MIT | https://mochajs.org/ | core/package.json |
| NYC | Code Coverage | ISC | https://github.com/istanbuljs/nyc | core/package.json |

## Data & API Libraries

| Software | Type | License | URL | Location |
|----------|------|---------|-----|----------|
| MongoDB Node.js Driver | Database Driver | Apache-2.0 | https://github.com/mongodb/node-mongodb-native | core/package.json |
| Async | Asynchronous Utilities | MIT | https://caolan.github.io/async/ | core/package.json |
| Request (countly-request) | HTTP Client | MIT | Custom implementation | core/package.json |
| JSON2CSV | JSON to CSV Converter | MIT | https://github.com/zemirco/json2csv | core/package.json |
| LRU Cache | Caching Library | ISC | https://github.com/isaacs/node-lru-cache | core/package.json |
| Redis | Redis Client | MIT | https://github.com/redis/node-redis | core/package.json |

## Security & Authentication

| Software | Type | License | URL | Location |
|----------|------|---------|-----|----------|
| Argon2 | Password Hashing | MIT | https://github.com/ranisalt/node-argon2 | core/package.json |
| JsonWebToken | JWT Implementation | MIT | https://github.com/auth0/node-jsonwebtoken | core/package.json |
| CSRF | CSRF Protection | MIT | https://github.com/expressjs/csurf | core/package.json |

## Other Utilities

| Software | Type | License | URL | Location |
|----------|------|---------|-----|----------|
| Colors | Terminal Color Styling | MIT | https://github.com/Marak/colors.js | core/package.json |
| FS-Extra | Enhanced File System | MIT | https://github.com/jprichardson/node-fs-extra | core/package.json |
| Lodash | Utility Library | MIT | https://lodash.com/ | core/package.json |
| Puppeteer | Headless Browser | Apache-2.0 | https://pptr.dev/ | core/package.json |
| WebFont | Web Font Loader | Apache-2.0 | https://github.com/typekit/webfontloader | core/frontend/express/public/javascripts/utils/webfont.js |
| JSTZ | Timezone Detection | MIT | https://github.com/iansinnott/jstz | core/frontend/express/public/javascripts/utils/jstz.min.js |
| Store.js | Local Storage | MIT | https://github.com/marcuswestin/store.js | core/frontend/express/public/javascripts/utils/store+json2.min.js |
| Tooltipster | Tooltip Library | MIT | https://github.com/iamceege/tooltipster | core/frontend/express/public/javascripts/utils/tooltipster/ |