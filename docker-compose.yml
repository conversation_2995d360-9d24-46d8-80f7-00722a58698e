# docker-compose.yml
services:
  mongodb:
    image: mongodb/mongodb-community-server:latest
    command: [
      "bash", "-c",
      "mongod --replSet rs0 --bind_ip_all --wiredTigerCacheSizeGB 1 \
      --setParameter diagnosticDataCollectionEnabled=true \
      --setParameter flowControlMaxSamples=10 \
      & pid=$!; \
      sleep 5; \
      until mongosh --eval 'db.adminCommand(\"ping\")' >/dev/null 2>&1; do sleep 2; done; \
      mongosh --eval 'rs.initiate({_id:\"rs0\",members:[{_id:0,host:\"mongodb:27017\"}]})'; \
      wait $pid"
    ]
    environment:
      - MONGODB_ENABLE_FREE_MONITORING=true
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4317
      - OTEL_SERVICE_NAME=mongodb
      - OTEL_TRACES_SAMPLER=parentbased_traceidratio
      - OTEL_TRACES_SAMPLER_ARG=1
    volumes:
      - mongodb_data:/data/db
      - ./observability/mongod.conf:/etc/mongod.conf
    ports:
      - "27017:27017"
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  mongodb-exporter:
    image: percona/mongodb_exporter:0.43.1
    command:
      - '--mongodb.uri=mongodb://mongodb:27017'
      - '--mongodb.collstats-colls=db.collection'
      - '--mongodb.direct-connect=false'
      - '--web.listen-address=:9216'
      - '--compatible-mode'
      - '--collector.diagnosticdata'
      - '--collector.replicasetstatus'
      - '--collector.dbstats'
      - '--collector.topmetrics'
      - '--collector.indexstats'
      - '--collector.collstats'
    ports:
      - "9216:9216"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9216/metrics"]
      interval: 10s
      timeout: 5s
      retries: 3
    depends_on:
      mongodb:
        condition: service_healthy

  dev-build:
    build:
      context: .
      dockerfile_inline: |
        FROM node:22
        WORKDIR /opt/countly
        # Update packages, install dependencies (including rsync), and clean up
        RUN apt-get update && \
            apt-get install -y --no-install-recommends \
            python3 \
            build-essential \
            rsync \
            python3-pip \
            sqlite3 \
            libsqlite3-dev && \
            rm -rf /var/lib/apt/lists/*
        
        # Copy core and plugins directories into the image
        COPY ./core /opt/countly/core/
        COPY ./plugins /opt/countly/plugins/
        COPY ./observability/otel.js /opt/countly/core/otel.js
        COPY ./observability/otelexpress.js /opt/countly/core/otelexpress.js
        
        # Ensure the target plugins directory exists in the core tree and copy plugins using rsync.
        RUN mkdir -p /opt/countly/core/plugins && \
            rsync -aL --exclude='data-manager/' /opt/countly/plugins/ /opt/countly/core/plugins/ && \
            rsync -aL /opt/countly/plugins/data-manager/ /opt/countly/core/plugins/data-manager/ && \
            ln -sf /usr/local/bin/node /usr/local/bin/nodejs
        #install otel
        RUN cd /opt/countly && npm install \
            @opentelemetry/api \
            @opentelemetry/auto-instrumentations-node \
            @opentelemetry/exporter-metrics-otlp-http \
            @opentelemetry/exporter-trace-otlp-http \
            @opentelemetry/resources \
            @opentelemetry/sdk-metrics \
            @opentelemetry/sdk-node \
            @opentelemetry/core \
            @opentelemetry/sdk-trace-base \
            @opentelemetry/semantic-conventions \
            @opentelemetry/instrumentation-fs \
            @opentelemetry/instrumentation-runtime-node \
            pino \
            cd /opt/countly/core && npm install sqlite3 --build-from-source && npm cache clean --force && rm -rf /root/.npm/*

    volumes:
      - countly_code:/opt/countly
    environment:
      - COUNTLY_CONFIG__SYMLINKED=true
      - FORCE_NPM_INSTALL=true
      - COUNTLY_CONFIG_API_MONGODB_HOST=mongodb
      - NODE_ENV=development
      - NPM_CONFIG_LOGLEVEL=verbose
    command: |
      bash -c '
        ln -sf /usr/local/bin/node /usr/local/bin/nodejs

        echo "Setting up configuration files..."
        # Copy config files if they do not exist
        cp -n "/opt/countly/core/api/config.sample.js" "/opt/countly/core/api/config.js" || echo "Skipping api config - file may already exist"
        cp -n "/opt/countly/core/plugins/plugins.default.json" "/opt/countly/core/plugins/plugins.json" || echo "Skipping plugins config - file may already exist"
        cp -n "/opt/countly/core/frontend/express/config.sample.js" "/opt/countly/core/frontend/express/config.js" || echo "Skipping express config - file may already exist"
        cp -n "/opt/countly/core/frontend/express/public/javascripts/countly/countly.config.sample.js" "/opt/countly/core/frontend/express/public/javascripts/countly/countly.config.js" || echo "Skipping countly config - file may already exist"

        # Update configurations
        # sed -i "s#countlyGlobal.path#\"http://localhost:3001\"#g" "/opt/countly/core/frontend/express/public/javascripts/countly/countly.config.js"
        sed -i "/mongodb: {/,/^[[:space:]]*}/ s/host: \"localhost\"/host: \"mongodb\"/g" "/opt/countly/core/frontend/express/config.js"
        sed -i "/web: {/,/^[[:space:]]*}/ s/host: \"localhost\"/host: \"0.0.0.0\"/g" "/opt/countly/core/frontend/express/config.js"
        sed -i "/api: {/,/^[[:space:]]*}/ s/host: \"localhost\"/host: \"0.0.0.0\"/g" "/opt/countly/core/api/config.js"
        sed -i "/mongodb: {/,/^[[:space:]]*}/ s/host: \"localhost\"/host: \"mongodb\"/g" "/opt/countly/core/api/config.js"
        sed -i "s/max_pool_size: 500/max_pool_size: 10/g" "/opt/countly/core/api/config.js"

        echo "Installing core dependencies..."
        cd /opt/countly/core
        npm install

        # Ensure plugins directory exists (redundant if already created during build, but safe)
        mkdir -p /opt/countly/core/plugins

        echo "Disabling specified plugins..."
        DISABLED_PLUGINS="ab-testing active_directory adjust browser cognito crashes-jira empty enterpriseinfo hooks ip_store ldap my-countly oidc okta old-ui-compatibility push push_approver recaptcha tracker two-factor-auth vue-example white-labeling"
        PLUGIN_JS_PATH="/opt/countly/core/bin/commands/scripts/plugin.js"
        cd /opt/countly/core/plugins
        for plugin_dir in */; do
          if [ -d "$plugin_dir" ]; then
            plugin=$(basename "$plugin_dir")
            is_disabled=0
            for disabled_plugin in $DISABLED_PLUGINS; do
              if [ "$plugin" = "$disabled_plugin" ]; then
                is_disabled=1
                break
              fi
            done
            if [ "$is_disabled" -eq 1 ]; then
              echo "Disabling $plugin..."
              if node --preserve-symlinks --preserve-symlinks-main "$PLUGIN_JS_PATH" disable "$plugin"; then
                echo "Successfully disabled $plugin"
              else
                echo "Failed to disable $plugin"
                exit 1
              fi
            else
              echo "Enabling $plugin..."
              if node --preserve-symlinks --preserve-symlinks-main "$PLUGIN_JS_PATH" enable "$plugin"; then
                echo "Successfully enabled $plugin"
              else
                echo "Failed to enable $plugin"
                exit 1
              fi
            fi
          fi
        done

        echo "Running build..."
        npx grunt dist-all

        echo "Initialization completed successfully"
      '
    depends_on:
      mongodb:
        condition: service_healthy

  countly-api:
    image: node:22
    build:
      context: .
      dockerfile_inline: |
        FROM node:22
        WORKDIR /opt/countly
        
        # Install build dependencies
        RUN apt-get update && \
            apt-get install -y --no-install-recommends \
            python3 \
            build-essential \
            sqlite3 \
            libsqlite3-dev && \
            rm -rf /var/lib/apt/lists/*
            
        # Pre-install sqlite3 globally
        RUN npm install -g sqlite3 --build-from-source

    working_dir: /opt/countly
    environment:
      - COUNTLY_CONFIG__SYMLINKED=true
      - COUNTLY_CONFIG_API_MONGODB_HOST=mongodb
      - NODE_OPTIONS=--require /opt/countly/core/otel.js --max-old-space-size=2048
      - OTEL_SERVICE_NAME=countly-api
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4318
      - OTEL_TRACES_SAMPLER=parentbased_traceidratio
      - OTEL_TRACES_SAMPLER_ARG=1
      - OTEL_METRIC_EXPORT_INTERVAL_MILLIS=15000
      - OTEL_EXPORTER_OTLP_TIMEOUT=10000
      - OTEL_DEBUG=false
    volumes:
      - countly_code:/opt/countly
    ports:
      - "3001:3001"
    command: bash -c "cd /opt/countly/core && node --preserve-symlinks --preserve-symlinks-main api/api.js" #   node --preserve-symlinks --preserve-symlinks-main api/api.js
    depends_on:
      mongodb:
        condition: service_healthy
      dev-build:
        condition: service_completed_successfully

  countly-frontend:
    image: node:22
    working_dir: /opt/countly
    environment:
      - COUNTLY_CONFIG__SYMLINKED=true
      - COUNTLY_CONFIG_FRONTEND_MONGODB_HOST=mongodb
      - NODE_OPTIONS= --require /opt/countly/core/otelexpress.js --max-old-space-size=2048
      - OTEL_SERVICE_NAME=countly-frontend
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4318
      - OTEL_TRACES_SAMPLER=parentbased_traceidratio
      - OTEL_TRACES_SAMPLER_ARG=1
      - OTEL_METRIC_EXPORT_INTERVAL_MILLIS=5000
      - OTEL_DEBUG=false
    volumes:
      - countly_code:/opt/countly
    ports:
      - "6001:6001"
    command: node --preserve-symlinks --preserve-symlinks-main core/frontend/express/app.js
    depends_on:
      mongodb:
        condition: service_healthy
      dev-build:
        condition: service_completed_successfully

  countly-jobserver:
    image: node:22
    working_dir: /opt/countly
    environment:
      - COUNTLY_CONFIG__SYMLINKED=true
      - COUNTLY_CONFIG_API_MONGODB_HOST=mongodb
      - NODE_OPTIONS= --require /opt/countly/core/otel.js --max-old-space-size=2048
      - OTEL_SERVICE_NAME=countly-jobs
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4318
      - OTEL_TRACES_SAMPLER=parentbased_traceidratio
      - OTEL_TRACES_SAMPLER_ARG=1
      - OTEL_METRIC_EXPORT_INTERVAL_MILLIS=5000
      - OTEL_DEBUG=false

    volumes:
      - countly_code:/opt/countly
    command: node --preserve-symlinks --preserve-symlinks-main /opt/countly/core/jobServer/index.js
    depends_on:
      mongodb:
        condition: service_healthy
      dev-build:
        condition: service_completed_successfully

  nginx:
    build:
      context: .
      dockerfile_inline: |
        FROM ubuntu:22.04
        
        # Install prerequisites
        RUN apt-get update && apt-get install -y \
            gnupg2 \
            curl \
            ca-certificates \
            wget \
            lsb-release \
            apt-transport-https
        
        # Add the official NGINX repository (per https://nginx.org/en/linux_packages.html#distributions)
        RUN echo "deb http://nginx.org/packages/ubuntu/ $(lsb_release -cs) nginx" > /etc/apt/sources.list.d/nginx.list && \
            echo "deb-src http://nginx.org/packages/ubuntu/ $(lsb_release -cs) nginx" >> /etc/apt/sources.list.d/nginx.list && \
            curl -fsSL https://nginx.org/keys/nginx_signing.key | apt-key add -
        
        # Update package list and install NGINX along with the OpenTelemetry module package
        RUN apt-get update && apt-get install -y nginx nginx-module-otel && \
            rm -rf /var/lib/apt/lists/*
        
        # Copy custom NGINX configuration files.
        # Ensure your nginx.conf (or default.conf) includes:
        #     load_module /usr/lib/nginx/modules/ngx_http_opentelemetry_module.so;
        COPY ./observability/nginx.conf /etc/nginx/nginx.conf
        COPY ./observability/default.conf /etc/nginx/conf.d/default.conf
        
        EXPOSE 80
        CMD ["nginx", "-g", "daemon off;"]

    environment:
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4318
      - OTEL_SERVICE_NAME=nginx-gateway
      - OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
      - OTEL_TRACES_SAMPLER=parentbased_traceidratio
      - OTEL_TRACES_SAMPLER_ARG=1.0
      - OTEL_PROPAGATORS=traceparent,baggage
      - NGINX_ACCESS_LOG_FORMAT=json_combined
      - NGINX_ERROR_LOG_LEVEL=warn
    ports:
      - "80:80"
    depends_on:
      otel-collector:
        condition: service_started
      countly-api:
        condition: service_started
      countly-frontend:
        condition: service_started
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        tag: "{{.Name}}"

# observability stack
  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
      - "9096:9096"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./observability/loki.yaml:/etc/loki/local-config.yaml
      - loki_data:/loki

  promtail:
    image: grafana/promtail:latest
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./observability/promtail.yaml:/etc/promtail/config.yml
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./observability/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - --config.file=/etc/prometheus/prometheus.yml
      - --storage.tsdb.path=/prometheus
      - --web.console.libraries=/usr/share/prometheus/console_libraries
      - --web.console.templates=/usr/share/prometheus/consoles
      - --log.level=error

  tempo:
    image: grafana/tempo:latest
    command: [ "-config.file=/etc/tempo.yaml" ]
    user: "root"
    volumes:
      - ./observability/tempo.yaml:/etc/tempo.yaml
      - tempo_data:/tmp/tempo
    ports:
      - "3200:3200"   # HTTP UI
      - "9098:9098"   # OTLP gRPC
      - "9099:9099"   # OTLP HTTP
      - "7946:7946"   # memberlist
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3200/ready"]
      interval: 10s
      timeout: 5s
      retries: 5

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    volumes:
      - ./observability/grafana/datasources.yaml:/etc/grafana/provisioning/datasources/datasources.yaml
      - grafana_data:/var/lib/grafana
    environment:
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin

  otel-collector:
    image: otel/opentelemetry-collector-contrib:latest
    command: ["--config=/etc/otel-collector.yaml"]
    volumes:
      - ./observability/otel-collector.yaml:/etc/otel-collector.yaml
    ports:
      - "8888:8888"   # Collector metrics
      - "8889:8889"   # Prometheus exporter
      - "4317:4317"   # OTLP gRPC
      - "4318:4318"   # OTLP HTTP
    depends_on:
      tempo:
        condition: service_healthy
      prometheus:
        condition: service_started
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:13133/health"]
      interval: 10s
      timeout: 10s
      retries: 10

volumes:
  mongodb_data:
  countly_code:
  loki_data:
  prometheus_data:
  tempo_data:
  grafana_data:
