{"name": "countly-request", "version": "1.0.0", "description": "Creates a wrapper for got with same signature with deprecated request in order to replace request package", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/Countly/countly-server.git"}, "scripts": {"test": "mocha test/countly-request.test.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"got": "^11.8.5", "hpagent": "^1.2.0"}, "devDependencies": {"mocha": "^11.1.0", "should": "^13.2.3"}}