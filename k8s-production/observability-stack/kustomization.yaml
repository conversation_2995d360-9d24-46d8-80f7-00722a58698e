apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: countly-observability

resources:
# Observability namespace
- namespace.yaml
# Core observability components  
- alloy.yaml
- loki-stack.yaml
- prometheus-stack.yaml
- grafana.yaml
- tempo-stack.yaml
- pyroscope-stack.yaml
# Kubernetes infrastructure monitoring
- kube-state-metrics.yaml
- node-exporter.yaml
# Ingress for external access
- observability-ingress.yaml

generatorOptions:
  labels:
    app: countly-observability
    managed-by: kustomize

configMapGenerator:
# Observability configuration for local stack
- name: observability-config
  envs:
  - configs/observability.env
# Grafana configuration
- name: grafana-config
  envs:
  - configs/grafana.env
# Grafana dashboards (placeholder for future dashboard configs)
# - name: grafana-dashboards
#   files:
#   - dashboards/countly-overview.json
#   - dashboards/countly-traces.json
#   options:
#     labels:
#       grafana_dashboard: "1"

# No auth needed for local stack
secretGenerator:
- name: observability-auth
  literals:
  - PROMETHEUS_BEARER_TOKEN=""
  - LOKI_BEARER_TOKEN=""
  - TEMPO_AUTH_HEADER=""
  - PYROSCOPE_AUTH_HEADER=""

# No patches needed - all configuration is inline