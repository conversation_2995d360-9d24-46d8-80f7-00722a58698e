apiVersion: v1
kind: Pod
metadata:
  name: mongo-debug
  namespace: countly
spec:
  restartPolicy: Never
  containers:
  - name: mongo-shell
    image: mongo:8
    command: ["sleep", "3600"]
    env:
    # MongoDB connection variables from the secret
    - name: MONGO_HOST
      value: "localhost"
    - name: MON<PERSON>O_PORT
      value: "27017"
    - name: MONGO_USER
      value: "admin"
    - name: MON<PERSON>O_PASSWORD
      value: ""
    - name: MON<PERSON>O_DB
      value: "countly"
    - name: REPLICA_SET
      value: "rs0"