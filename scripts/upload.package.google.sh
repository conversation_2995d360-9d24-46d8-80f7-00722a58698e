#!/bin/bash
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
VERSION="$(grep -oP 'version:\s*"\K[-0-9a-zA-Z\.]*' "$DIR/../version.info.js")"

npm install @google-cloud/storage;

mkdir -p "$DIR/../../$VERSION"

cp -rf "$DIR"/../../countly-*.tar.gz "$DIR/../../$VERSION"

echo "$GOOGLE_BUCKET_KEY" > "$DIR/countly-01-b1e1e770429f.json"

node "$DIR"/upload.package.google.js "countly-ee" "$DIR/../../$VERSION"