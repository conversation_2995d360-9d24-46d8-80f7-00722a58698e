Getting package from repo

    apt-get install git -y
    cd /opt
    git clone https://github.com/Countly/countly-enterprise-plugins countly
    cd countly

Installing enterpise edition development

    cd /opt/countly
    bash scripts/countly.use.core.sh
    bash core/bin/countly.install.sh

Updating enterprise edition

    cd /opt/countly
    bash scripts/countly.use.core.sh

Preparing package

    cd /opt/countly
    bash scripts/prepare.ee.package.sh