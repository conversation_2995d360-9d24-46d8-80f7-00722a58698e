# DO NOT EDIT THIS FILE AS IT WILL BE OVERRIDEN DURING TRANSLATION SYNC
# IF YOU WOULD LIKE TO HELP COUNTLY TO BE TRANSLATED INTO YOUR LANGUAGE
# PLEASE VISIT https://www.transifex.com/osoner/countly

push.plugin-title = Push Notifications
push.plugin-description = Increase loyalty, user retention &amp; satisfaction with native iOS and Android push notifications
push.sidebar.section = Messaging
push.sidebar.overview = Overview
push.sidebar.messages = Messages

push.no-message = No message
push.create = Create Message
push.create-message-n = Create Message to {0} users

mgmt-plugins.push.here = here
mgmt-plugins.push.ios = iOS Settings
mgmt-plugins.push.ios.type = Authentication type
mgmt-plugins.push.ios.type.desc = Choose between Token-based authentication (P8 file) & Sandbox + Production certificate (P12 file)
mgmt-plugins.push.ios.apn_token = Key file (P8)
mgmt-plugins.push.ios.apn_token.desc = Token-based authentication file you downloaded from Apple.
mgmt-plugins.push.ios.key = Key ID
mgmt-plugins.push.ios.key.desc = ID of a P8 key, usually it's encoded in P8 filename.
mgmt-plugins.push.ios.team = Team ID
mgmt-plugins.push.ios.team.desc = ID of the team your app is registered to. You can get one from
mgmt-plugins.push.ios.bundle = Bundle ID
mgmt-plugins.push.ios.bundle.desc = Main ID of your application.
mgmt-plugins.push.ios.apn_universal = Sandbox + Production certificate (P12)
mgmt-plugins.push.ios.apn_universal.desc = Your universal certificate file you downloaded from Apple.
mgmt-plugins.push.ios.pass = Passphrase
mgmt-plugins.push.ios.pass.desc = Secret password you've set when exporting P12 file from Keychain Access.
mgmt-plugins.push.android = Android (Google FCM)
mgmt-plugins.push.android.key = Firebase key
mgmt-plugins.push.android.key.desc = Your server key, you can get one from
mgmt-plugins.push.h = Android (Huawei Push Kit)
mgmt-plugins.push.h.key = Huawei App ID
mgmt-plugins.push.h.key.desc = Your numerical Huawei App ID (Client ID)
mgmt-plugins.push.h.secret = App Secret
mgmt-plugins.push.h.secret.desc = Huawei App Secret, sign secret of your app can be found in code signing properties or at
mgmt-plugins.push.h.secret.hds = Huawei developer portal
mgmt-plugins.push.error.nokey = Please enter Key ID
mgmt-plugins.push.error.noteam = Please enter Team ID
mgmt-plugins.push.error.nobundle = Please enter application Bundle ID
mgmt-plugins.push.error.nofile = Please select key or certificate file
mgmt-plugins.push.error.h.key = Please enter Huawei App ID
mgmt-plugins.push.error.h.secret = Please enter Huawei App Secret
mgmt-plugins.push.error.h.keynum = Huawei App ID can contain only digits
mgmt-plugins.push.uploaded = Download file uploaded previously
mgmt-plugins.push.uploaded.p12 = Universal certificate (p12) is already uploaded
mgmt-plugins.push.uploaded.p8 = Key file (p8) is already uploaded
mgmt-plugins.push.uploaded.bundle = Bundle id of this certificate:
mgmt-plugins.push.detected = Detected key type:
mgmt-plugins.push.rate = Rate limit
mgmt-plugins.push.rate.rate = Maximum number of notifications scheduled per period
mgmt-plugins.push.rate.help = Push notifications sent to FCM and APNS will separately be rate limited based on this configuration for each push notification campaign
mgmt-plugins.push.rate.period = Period duration (seconds)
mgmt-plugins.push.rate.period.help = Timeframe to apply the rate limit to

systemlogs.action.push_message_created = Push Message Created
systemlogs.action.push_credentials_update = Push Credentials Updated
systemlogs.action.push_message_deleted = Push Message Deleted

# Help
help.manage-apps.push-gcm-key = Server API Key for Firebase Cloud Messaging (FCM). It's required if you're going to send push notifications to your Android app users. You can get one from Firebase Console.
help.mgmt-plugins.push.ios.type = Countly supports 2 types of authentication with APN: Token-based (p8 file, JWT) & Sandbox + Production certificate (p12 file).

help.dashboard.push.sent = Total number of messages sent for this application within selected period of time.
help.dashboard.push.actions = Number of actions performed for this app. Action represents a positive reaction of a user to your message. Please see SDK guide to know how to enable this metric in your app.
help.dashboard.total-users-push = Total number of users of this app.
help.dashboard.messaging-users = A number of users who agreed to receive push notifications, that is a number of users with APN or FCM tokens stored in the database.

pu.po.title = Create Push Notification
pu.po.desc = Compose your push notification in 4 easy steps

pu.po.view.title = Push Notification
pu.po.view.desc = Message details

pu.po.preview = Preview
pu.po.preview.help = Please note that this is a preview screen. What you see here may not be same as what end users see on their smartphones due to varying device sizes.

pu.po.tt.pers = Add personalized content
pu.po.tt.emoji = Add emoji

pu.po.tab0.title = Apps & Platforms
pu.po.tab0.title.auto = Platforms
pu.po.tab0.desc = Destinations

pu.po.tab1.title = Scheduling
pu.po.tab1.desc = Set message date
pu.po.tab1.title.auto = Campaign Rules
pu.po.tab1.desc.auto = Triggers & dates

pu.po.tab1.chr = Cohorts
pu.po.tab1.chr-desc = Send to the users currently in selected cohorts

pu.po.tab1.aud = Audience Selection
pu.po.tab1.now.t = Now
pu.po.tab1.now.d = Select users now
pu.po.tab1.later.t = Before sending
pu.po.tab1.later.d = Select users right before sending the message

pu.po.tab2.ddc = Delivery Date Calculation
pu.po.tab2.ddc.arr = Relative to the date event arrived to the server
pu.po.tab2.ddc.evt = Relative to the date event occurred on a device
# pu.po.tab2.ddc.h = In "event occurrence" case Countly only sends notifications within 30 minutes of actual event recording date, discarding any "late" notifications which could be triggered due to event arriving to the server with a delay because of networking issues.
# pu.po.tab2.ddc.h = Note that larger amount of notifications won't be sent in case of event occurence date since an event might arrive to the server with a delay due to networking issues. On the other hand, accuracy of "event occurence" case is better, since there won't be any late notifications.
# pu.po.tab2.ddc.arr = Relative to the event arrival to the server date
# pu.po.tab2.ddc.evt = Relative to the event occurence on the device date

pu.po.tab2.trc = Behaviour when trigger condition is no longer met
pu.po.tab2.trc.true = Cancel when user exits selected cohort(s)
pu.po.tab2.trc.false = Cancel when user enters selected cohort(s) back
pu.po.tab2.trc.dont = Send anyway

pu.po.tab2.title.auto = Message Delivery
pu.po.tab2.desc.auto = Periodicity & capping

pu.po.compose.title = Message
pu.po.compose.desc = Compose content

pu.po.review.title = Review
pu.po.review.desc = Check your message

pu.po.tab0.apps = Apps
pu.po.tab0.add = Add App
pu.po.tab0.select-apps = Select Apps
pu.po.tab0.select-apps-desc = Only apps with APN or FCM credentials are displayed
pu.po.tab0.select-platforms = Select Platforms
pu.po.tab0.select-platforms-no = Selected app doesn't have APN or FCM credentials
pu.platform.i = iOS
pu.platform.a = Android
pu.po.prev = Previous step
pu.po.next = Next step
pu.po.send = Send!
pu.po.start = Start!
pu.po.edit = Save
pu.po.close = Close
pu.po.duplicate = Duplicate
pu.po.delete = Delete

pu.po.tab1.testing = Testing
pu.po.tab1.testing-desc = Send to production users or to test users?
pu.po.tab1.testing-prod = Production Users
pu.po.tab1.testing-test = Test Users
pu.po.tab1.testing-test-desc = Development & test users only

pu.po.tab1.scheduling = Scheduling
pu.po.tab1.scheduling-desc = Send now or schedule sending for later
pu.po.tab1.scheduling-now = Send now
pu.po.tab1.scheduling-auto-now = Start now
pu.po.tab1.scheduling-now-desc = Sending will start once you click Send
pu.po.tab1.scheduling-date = Schedule
pu.po.tab1.scheduling-date-desc = Set specific date & time for this message
pu.po.tab1.tz = Send according to user's timezone?
pu.po.tab1.tz-desc = Message can either be sent to all users at once using date and time you specified or it can be sent to each user in their timezones.
pu.no = No
pu.po.tab1.tz-no-desc = Send all notifications at once
pu.po.tab1.tz-yes = Send in user timezones
pu.po.tab1.tz-yes-desc = Send each notification in timezone of the user it's for
pu.po.tab1.tz-yes-help = Only SDK 16.12+ (iOS & Android native SDKs, check documentation for others) sends timezone information to the server. Server will use default app timezone in case SDK haven't reported timezone yet.

pu.po.tab1.geos = Geolocations
pu.po.tab1.geos-desc = Filter users by their last known location?
pu.po.tab1.geos.no = No filtering


pu.po.recipients.s = Recipient: {0}
pu.po.recipients.m = Recipients: {0}
pu.po.recipients.temporary = Number of users calculation is still running, it can take up to several minutes in tough cases. Feel free to send your message without waiting.

pu.po.tab2.message.type = Message type
pu.po.tab2.extras = Additional options

pu.type.message = Message
pu.type.data = Data-only
pu.type.rich = Rich

pu.po.loading = We're counting your users
pu.po.loading-desc = Please wait, it will be done soon

pu.po.sending = Saving
pu.po.sending-desc = Just a moment

pu.po.sent = Saved
pu.po.sent-desc = All good!

pu.po.tab2.placeholder = Write your message here (default is required, add more localized messages if needed)
pu.po.tab2.default-message = Your message
pu.po.tab2.default-message.invalid = Default message is required
pu.po.tab2.default-button-title.invalid = Default button title is required
pu.po.tab2.default-button-link.invalid = Default button link is required
pu.po.tab2.extras.sound = Send sound
pu.po.tab2.extras.sound.invalid = Sound must be a string
pu.po.tab2.extras.badge = Add badge
pu.po.tab2.extras.badge.invalid = Badge must be a valid number
pu.po.tab2.extras.badge.help = iOS supports badges by default, Android requires additional steps (more details in our Android SDK Guide)
pu.po.tab2.extras.media = Attachment URL
pu.po.tab2.extras.media.placeholder = Image URL
pu.po.tab2.extras.media.invalid = Must be a valid URL to an image (JPG, PNG) or animation (GIF) less than 10MB in size, audio (MP3, WAV, AIF) less than 5MB in size or video (MPEG, MPEG2, MPEG4) less than 50MB in size
pu.po.tab2.extras.media.toobig = Media attachment must be less than 10MB for images and animations, less than 5MB for audio & 50MB for videos
pu.po.tab2.extras.media.help = Media attachments are fully supported on iOS 10+. Android supports only images, iOS 9- doesn't support media at all. In case media is not supported, users will only see message text.
pu.po.tab2.extras.url = On click URL
pu.po.tab2.extras.url.invalid = URL must be valid
pu.po.tab2.extras.url.help = Default URL or deeplink which is being open when user taps a message in drawer
pu.po.tab2.extras.data = Send JSON
pu.po.tab2.extras.data.invalid = This field must contain valid JSON string
pu.po.tab2.extras.data.placeholder = JSON string
pu.po.tab2.extras.data.help = App-specific JSON data can be sent along with standard content
pu.po.tab2.mtitle = Message title
pu.po.tab2.mtitle.placeholder = Optional title for your message
pu.po.tab2.mtext = Message text
pu.po.tab2.mbtn = Buttons
pu.po.tab2.mbtns = Number of buttons
pu.po.tab2.mbtn.urls = Button URLs
pu.po.tab2.mbtn.req = Button title required
pu.po.tab2.mbtn.url = Must be a valid URL
pu.po.tab2.mbtn.0 = Button 0
pu.po.tab2.mbtn.1 = Button 1
pu.po.tab2.mbtn.2 = Button 2
pu.po.tab2.btntext = Button text
pu.po.tab2.urlordeep = URL or deeplink
pu.po.tab2.rich = Rich notification options
pu.po.tab2.mmedia = Media
pu.po.tab2.mmedia.type.image = Image
pu.po.tab2.mmedia.type.anima = Animation
pu.po.tab2.mmedia.type.audio = Audio
pu.po.tab2.mmedia.type.video = Video
pu.po.tab2.mmedia.type.warn.android = This type of media is not supported on Android
pu.po.tab2.mmedia.status = Request status:
pu.po.tab2.default-message.help = Default localization for all users. You can override default message & button names by setting them for specific locales.
pu.po.no-users = Sorry, there are no push-enabled users for specified options.
pu.po.no-users-try-change = Try to change
pu.po.no-users-try-change-apps = platforms or production-test switch
pu.po.no-users-start-over = Start over
pu.po.tab2.variable = Variable
pu.po.tab2.capital = Make variable value start from capital letter
pu.po.tab2.fallback = Fallback value
pu.po.tab2.help = Default value which will be used in case user profile doesn't have this variable
pu.po.tab2.props = User Properties
pu.po.tab2.cust = Custom Variables
pu.po.tab2.varpl = Select variable
pu.po.tab2.fallpl = Enter fallback value
pu.po.tab2.tt = User''s "{0}" property which falls back to "{1}"

datepicker.dt.click = Set date & time
datepicker.tz = Send in user's timezone
datepicker.clear = Clear
datepicker.apply = Apply
datepicker.pick-time = Pick Time

pu.locale.default = Default
pu.locale.null = Unknown
pu.po.tab3.review = Review
pu.po.tab3.apps = Apps
pu.po.tab3.platforms = Platforms
pu.po.tab3.date = Date
pu.po.tab3.date.now = At the time of message creation
pu.po.tab3.date.intz = ; in user timezones
pu.po.tab3.unknown = Unknown
pu.po.tab3.type = Type
pu.po.tab3.type.message = Text message
pu.po.tab3.type.data = Data
pu.po.tab3.type.update = Update (deprecated)
pu.po.tab3.type.review = Review (deprecated)
pu.po.tab3.type.category = Category (deprecated)
pu.po.tab3.type.link = URL (deprecated)
pu.po.tab3.test = Send to test devices
pu.po.tab3.test.true = Yes
pu.po.tab3.test.false = No
pu.po.tab3.btns = Buttons
pu.po.tab3.media = Media
pu.po.tab3.extras = Standard options

pu.po.tab3.extras.sound = Sound
pu.po.tab3.extras.badge = iOS badge
pu.po.tab3.extras.url = On click URL
pu.po.tab3.extras.data = Extra data
pu.po.tab3.extras.media = Media

pu.po.tab3.date.date = Scheduled to be sent on
pu.po.tab3.date.sent = Sent on
pu.po.tab3.date.tz = Sent in user timezone

pu.po.tab3.audience = Recipients

pu.po.tab3.errors = Errors
pu.po.tab3.errors.code = Code
pu.po.tab3.errors.message = Error
pu.po.tab3.errors.count = Count

pu.po.confirm = Confirm sending
pu.po.confirm.ready = Iâm ready to send this message
pu.po.confirm.s = to {0} receipient
pu.po.confirm.m = to {0} receipients
pu.po.recipients.message = {0} users will receive this campaign after it is started.
pu.po.recipients.message.details = This message will automatically be delivered to app users as they match campaign trigger criteria.
pu.po.recipients.message.edit = Message modification only affects notifications scheduled after modification takes place.

pu.dash.users = Users
pu.dash.users.total = Total Users
pu.dash.users.enabl = Messaging-enabled Users
pu.dash.totals = Overall
pu.dash.metrics = Metrics
pu.dash.metrics.sent = Messages sent
pu.dash.metrics.acti = Actions performed
pu.dash.create = Create Message
pu.dash.monthly = Monthly
pu.dash.weekly = Weekly
pu.dash.messages = Messages
pu.dash.messages.all = All
pu.dash.messages.api = API
pu.dash.messages.dash = Dashboard
pu.dash.actions = Actions
pu.dash.sent = Sent


pu.t.nothing = No records found
pu.t.search = Search by default message
pu.t.message = Message
pu.t.apps = Apps
pu.t.status = Status
pu.t.created = Created
pu.t.sent-scheduled = Date sent / Scheduled
pu.t.result = Sent / Actioned

push.message.status.initial = Initial
push.message.status.created = Created
push.message.status.scheduled = Scheduled
push.message.status.sending = Sending
push.message.status.sending-errors = Sending with errors
push.message.status.aborted = Aborted
push.message.status.sent = Sent
push.message.status.sent-errors = Sent with errors

push.message.status.auto.true = Active
push.message.status.auto.false = Inactive

push.error.econnrefused = Countly cannot connect to APN/FCM. Please check your proxy server settings (if any) and / or server connectivity to APN/FCM servers.
push.error.timeout = Connection timed out. Please ensure that server has stable connection to APN and/or FCM.
push.error.crash = Process crashed while sending this messsage. Please contact support.
push.error.exited = Process exited while sending this messsage. Please contact support.
push.error.exited-sent = Process exited while sending this messsage due to connection being unexpectedly closed by APNS. Some notifications might have been sent twice (up to 500).
push.error.3-eof = APNS connection error. Possible reasons include: invalid certificate / auth key, firewall block, system root certificates (CA) issues.
push.error.no-credentials = You don't have credentials for any of selected app-platform combinations.
push.error.no-app-credentials = This application doesn't have any push notification credentials set.
push.error.no-cohorts = Automated messages depend on Cohorts and Events, yet you don't have any of those.
push.error.cohorts-deleted = This message cannot be activated since some of cohorts used it have been deleted.
push.error.no.cohorts = No Cohorts or events
push.error.no.credentials = No credentials
push.error.i.understand = OK, I understand


push.errorCodes.bug = You found a bug! Please contact support.
push.errorCodes = Errors:
push.errorCode.i400 = APN 400 Bad request
push.errorCode.i400+BadDeviceToken.desc = The push token received from your app by Countly Server was rejected by APNS as invalid. Please make sure you set `pushTestMode` property on the SDK's initial configuration correctly. Also make sure provisioning profile (Development or Distribution) is valid, bundle ID is correct and entitlements are properly set.
push.errorCode.i400+MissingTopic.desc = Server failed to parse the certificate, please ensure you use universal certificate and contact support if you do
push.errorCode.i400+DeviceTokenNotForTopic.desc = Probably APNS certificate doesn't correspond to the Bundle ID of your application
push.errorCode.i400+TopicDisallowed.desc = Sending notifications to this topic is not allowed. Apple rejects sending notifications to this topic, most probably there is no such app in Certificates, Identifiers & Profiles portal.
push.errorCode.i403 = APN 403 Certificate error
push.errorCode.i403.desc = Authentication error, please generate and upload new certificate from a Mac used to make this particual app build
push.errorCode.i403+InvalidProviderToken.desc = APN 403 error: InvalidProviderToken. Please check your Auth key file, Team ID & Bundle ID - some of those is invalid.
push.errorCode.i405 = APN 405 Bad method
push.errorCode.i405.desc = push.errorCodes.bug
push.errorCode.i413 = APN 413 Payload too large
push.errorCode.i413.desc = Message was too large and APN declined to deliver it.
push.errorCode.i429 = APN 429 Too many requests
push.errorCode.i429.desc = push.errorCodes.bug
push.errorCode.i500 = APN 500 Internal error
push.errorCode.i500.desc = Apple server unexpectedly returned HTTP error 500. Please try again later.
push.errorCode.i503 = APN 503 Shutdown
push.errorCode.i503.desc = Apple server unexpectedly decided to shutdown returning HTTP error 503. Please try again later.

push.errorCode.a200 = FCM 200 message error
push.errorCode.a200+MissingRegistration.desc = push.errorCodes.bug
push.errorCode.a200+InvalidRegistration.desc = Probably you modified the way SDK handles FCM tokens. Please ensure you do it right or contact support.
push.errorCode.a200+InvalidParameters.desc = Invalid request parameters. Please send server logs to Countly support.
push.errorCode.a200+MismatchSenderId.desc = Invalid Sender ID. Most probably SDK competes for tokens with other Firebase SDK you have in your app. Please override the way our SDK or another SDK get a token so they would end up using the same token.
push.errorCode.a200+MessageTooBig.desc = Message was too large and Google declined to deliver it.
push.errorCode.a200+InvalidDataKey.desc = Message contains invalid data key, please check:
push.errorCode.a200+InvalidTtl.desc = push.errorCodes.bug
push.errorCode.a200+DeviceMessageRateExceeded.desc = You send messages to the same device more often than Google allows, please do that less often.
push.errorCode.a200+TopicsMessageRateExceeded.desc = You send messages to the same topic more often than Google allows, please do that less often.
push.errorCode.a400 = FCM 400 JSON error
push.errorCode.a400.desc = push.errorCodes.bug
push.errorCode.a401 = FCM 401 Authentication error
push.errorCode.a400.desc = push.errorCodes.bug
push.errorCode.a500 = FCM 500 Internal error
push.errorCode.a500.desc = Google server unexpectedly returned HTTP error 500. Please try again later.
push.errorCode.a200+Unavailable.desc = Google server unexpectedly returned HTTP error 200+Unavailable. Please try again later.
push.errorCode.a200+InternalServerError.desc = Google server unexpectedly returned HTTP error 200+InternalServerError. Please try again later.
push.errorCode.a501 = FCM 501 Error
push.errorCode.a502 = FCM 502 Error
push.errorCode.a503 = FCM 503 Error
push.errorCode.link.i = Apple docs
push.errorCode.link.a = Google docs
push.errorCode.skiptz = Too late to send
push.errorCode.skiptz.desc = This much notifications have been discarded because time of arrival would be at least 60 minutes later than expected (either due to user's timezone, or due to networking issues).
push.errorCode.del = Message deleted
push.errorCode.del.desc = This much notifications have been discarded their message has been deleted.
push.errorCode.aborted = Aborted
push.errorCode.aborted.desc = This much messages have been removed from queue after an unrecoverable error. Please check error message above or contact support if there's no error message.
push.errorCode.consent = Consent cancelled
push.errorCode.consent.desc = This much messages have been removed from queue after user removed push consent.

push.totals.processed = Processed
push.totals.sent = Accepted
push.totals.errors = Errors

pu.po.users.s = {0} user
pu.po.users.m = {0} users
pu.po.metrics = Metrics
pu.po.metrics.processed = Processed
pu.po.metrics.processed.desc = How many notifications have been processed so far
pu.po.metrics.sent = Sent Successfully
pu.po.metrics.sent.desc = How many notifications have been accepted by APNS & FCM
pu.po.metrics.sent.none = No notifications were sent! Please check message errors
pu.po.metrics.sent.one = Your notification has been sent successfully!
pu.po.metrics.sent.all = All notifications have been sent successfully!
pu.po.metrics.actions = Actioned
pu.po.metrics.actions.desc = How many users performed push notification action
pu.po.metrics.actions.all = All users performed action! Good Job!
pu.po.metrics.actions.performed = performed action.
pu.po.metrics.actions0.performed = opened default URL.
pu.po.metrics.actions1.performed = clicked first button.
pu.po.metrics.actions2.performed = clicked second button.
pu.po.left.to.send.s = {0} notification left to send
pu.po.left.to.send.m = {0} notifications left to send
pu.po.left.to.send.none = All notifications are sent!
pu.po.left.to.send.batch = next batch will start at {0}
pu.po.expired.s = {0} token expired
pu.po.expired.m = {0} tokens expired
pu.po.unset.s = {0} tokens expired
pu.po.unset.m = {0} token expired
pu.po.errors.s = {0} notification wasn\'t sent due to an error
pu.po.errors.m = {0} notifications weren\'t sent due to an error

pu.apn.uni = Universal Certificate (Production & Sandbox)
pu.apn.none = None

pu.gcm.gcm = FCM Key
pu.gcm.none = None

pu.remove = Remove credentials
pu.validate = Validate
pu.validating = Validating credentials
pu.validation.error = Credentials error:
pu.remove = Remove credentials
pu.creds.none = None
pu.creds.type.apn_universal = APN Universal Certificate is set
pu.creds.type.apn_token = APN Auth Key is set
pu.creds.type.gcm = FCM Server API Key is set
pu.creds.set.gcm = Enter Firebase Server Key
pu.creds.cert = P12 Certificate
pu.creds.pass = Passphrase
pu.creds.auth_key = P8 Key File
pu.creds.key_id = Auth Key ID
pu.creds.team_id = Team ID
pu.creds.bundle_id = Bundle ID
pu.creds.apn.type.apn_universal = APN Certificate (Sandbox & Production)
pu.creds.apn.type.apn_token = APN Auth Key

userdata.push = Push tokens
pu.tk.id = APN Test token (development)
pu.tk.ia = APN Test token (ad hoc or TestFlight)
pu.tk.ip = APN Production token
pu.tk.at = FCM Test token
pu.tk.ap = FCM Production token
pu.tk.ht = Huawei Test token
pu.tk.hp = Huawei Production token

#Automated Push
pu.dash.btn-group.create-message = Create Message
pu.dash.btn-group.automated-message = Automated Message
pu.dash.btn-group.automated-message-desc = Create an automated push campaign to send a message to users when their-in-app behaviour matches defined criteria.
pu.dash.btn-group.automated-message-link = Learn more about automation
pu.dash.btn-group.one-time-message = One-time Message
pu.dash.btn-group.one-time-message-desc = Create a one-time push notification campaign to send right away on in the future.
pu.dash.btn-group-available-in-enterprise = Available in enterprise edition
pu.dash.btn.learn-more = Learn more about automation

pu.po.tab1.trigger-type = Trigger Type
pu.po.tab1.trigger-type.entry = Cohort Entry
pu.po.tab1.trigger-type.exit = Cohort Exit
pu.po.tab1.trigger-type.event = Performed Event
pu.po.tab1.cohort-entry-desc = Triggered when user enters into any of the cohorts you select
pu.po.tab1.cohort-exit-desc = Triggered when user exits from any of the cohorts you select
pu.po.tab1.cohort-event-desc = Triggered when user performs a selected event
pu.po.tab1.select-cohort = Select one or more cohorts to set a trigger
pu.po.tab1.select-cohort-desc = Recalculation of cohorts above will trigger message sending process automatically
pu.po.tab1.select-cohort-placeholder = Select cohorts(s)
pu.po.tab1.select-event = Select one or more events to set a trigger
pu.po.tab1.select-event-placeholder = Select event(s)
pu.po.tab1.campaign-start-date = Campaign Start Date
pu.po.tab1.additional-options = Additional Options
pu.po.tab1.campaign-end-date = Campaign end date

pu.po.tab2.delivery-method = Delivery method
pu.po.tab2.delivery-method-desc = Choose when your message should be delivered
pu.po.tab2.immediately = Immediately
pu.po.tab2.immediately-desc = Deliver the message as soon as triggering cohort is recalculated
pu.po.tab2.immediately-desc-event = Deliver the message as soon as triggering event reached the server
pu.po.tab2.delayed = Delayed
pu.po.tab2.delivery-time = Delivery time
pu.po.tab2.delivery-time-desc = Set an optional delivery time for your message
pu.po.tab2.delivery-end-desc = Optional date when server should stop sending messages
pu.po.tab2.send-in-user-tz = Send in users' timezone at
pu.po.tab2.select-time = Select time
pu.po.tab2.capping = Capping
pu.po.tab2.capping-desc = Configure number of messages per user. Enabling this option will limit number of messages sent to the same user.
pu.po.tab2.capping.no = No capping
pu.po.tab2.capping.no-desc = Message is sent whenever user entered to or exited from the cohort
pu.po.tab2.capping.no-desc-event = Message is sent whenever user's event is received by the server
pu.po.tab2.capping.yes = Capped
pu.po.tab2.capping.yes-desc = Number of messages is limited
pu.po.tab2.message-per-user = Maximum messages per user
pu.po.tab2.message-per-user-desc = Total number of messages individual user can receive from this campaign
pu.po.tab2.sleep = Minimum time between messages
pu.po.tab2.sleep-desc = User will be eligible to receive a repetitive message only if she / he triggers the campaign conditions at least configured time after previous message.

pu.po.tab4.apps-platforms = Platforms
pu.po.tab4.app = App
pu.po.tab4.campaign-rules = Campaign Rules
pu.po.tab4.trigger-type = Trigger Type
pu.po.tab4.trigger-cohort-entry = Cohort entry
pu.po.tab4.trigger-cohort-exit = Cohort exit
pu.po.tab4.cohorts.no = Cohort(s) were deleted
pu.po.tab4.cohorts.s = Cohort
pu.po.tab4.cohorts.m = Cohorts ({0})
pu.po.tab4.events.no = Events(s) were deleted
pu.po.tab4.events.s = Event
pu.po.tab4.events.m = Events ({0})
pu.po.tab4.caping = Capping
pu.po.tab4.caping-message = At most {0} message is going to be sent per user
pu.po.tab4.message-content = Message Content
pu.po.tab4.message-title = Title
pu.po.tab4.message-text = Text
pu.po.tab4.message-sound = Sound
pu.po.tab4.message-media = Media
pu.po.tab4.message-badge = Badge

push.po.one-time-messages = ONE-TIME MESSAGES
push.po.automated-messages = AUTOMATED MESSAGES
push.po.table.dublicate = Duplicate
push.po.table.resend = Resend failed notifications
push.po.table.edit = Edit
push.po.table.delete = Delete
push.po.table.recipients = View recipients

pu.po.progress = Progress
pu.po.progress.auto = 30 days overview
pu.never = Never
pu.days.s = day
pu.days.m = days
pu.hours.s = hour
pu.hours.m =hours
pu.messages.s = message
pu.messages.m = messages
pu.messages.between = between messages
pu.min = Minimum
pu.max = Maximum
pu.enable = Enable
pu.ended = Campaign ended
pu.send-message = Send a Message
pu.send-message-desc = Send a push notification to all users matching your query criteria

push.proxyhost = Proxy Hostname
push.proxyport = Proxy Port
push.proxyuser = Proxy Username
push.proxypass = Proxy Password

push.note.gcm.t = Your push notifications can't be delivered
push.note.gcm.m = Following apps have GCM credentials instead of Firebase ones: <b>{0}</b>.<br/><br/>Usage of GCM is no longer possible since Google stopped accepting requests to GCM endpoint.<br/><br/>Please update app credentials to a valid FCM server key.

mail.autopush-error-subject = Countly Automated Push Problem
mail.autopush-error = Hi {0},<br/><br/>Your <a href="{1}">automated message</a> cannot be sent due to a repeating error. Please review message status and reactivate the message once the problem is resolved.<br/><br/>Best,<br/>A fellow Countly Admin
internal-events.[CLY]_push_sent = Push sent
internal-events.[CLY]_push_action = Push action
