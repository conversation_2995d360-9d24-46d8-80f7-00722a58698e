push-notification.title = Push Notifications
push-notification.description = An overview of all push notifications sent and actions performed in response.
push-notification.one-time = One-time Notifications
push-notification.automated = Automated Notifications
push-notification.transactional = API Notifications
push-notification.total-app-users = Total App Users
push-notification.enabled-users = Notification-enabled Users
push-notification.enabled-users-percentage = Enabled Users Percentage
push-notification.enabled-users-percentage-description = Number of users who have agreed to receive notifications, expressed as a percentage over the total number of app users.
push-notification.platform-filter-label-one-time = One-time notifications for
push-notification.platform-filter-label-automatic = Automatic notificatons for
push-notification.platform-filter-label-transactional = Transactional notifications for
push-notification.platform-filter-all = All Platforms
push-notification.platform-filter-android = Android
push-notification.platform-filter-ios = IOS
push-notification.status-filter-all = All Messages
push-notification.create-button = New Message
push-notification.time-period = TIME PERIOD
push-notification.created-by = Created by
push-notification.unknown-error = Unknown error occurred. Please try again later or contact support team.
push-notification.sent-serie-name = Notifications Sent
push-notification.sent-serie-description = Total number of notifications sent in the selected time period.
push-notification.actions-performed-serie-name = Actions Performed
push-notification.actions-performed-serie-description = Total number of actions performed by users in response to notifications sent, in the selected time period.
push-notification.table-notification-name = Notification name
push-notification.table-status = Status
push-notification.table-created = Created
push-notification.table-date-sent = Date Sent/Scheduled
push-notification.table-sent = Sent
push-notification.table-actioned = Actioned
push-notification.table-created-by = Created By
push-notification.table-message-content = Message Content
push-notification.duplicate = Duplicate
push-notification.delete = Delete
push-notification.resend = Resend
push-notification.approve = Approve
push-notification.reject = Reject
push-notification.start = Start
push-notification.stop = Stop
push-notification.edit-draft = Edit Draft
push-notification.edit = Edit
push-notification.sent = Sent
push-notification.sending = Sending
push-notification.created = Created
push-notification.draft = Draft
push-notification.waiting-for-approval = Waiting for approval
push-notification.aborted = Aborted
push-notification.failed = Failed
push-notification.stopped = Stopped
push-notification.scheduled = Scheduled
push-notification.rejected = Rejected
push-notification.time-chart-period-weekly = Weekly
push-notification.time-chart-period-monthly = Monthly
push-notification.time-chart-period-daily= Daily
push-notification.now = Now
push-notification.right-before-sending-the-message = Right before sending the message
push-notification.segmented-push-enabled-users = Segmented push-enabled users
push-notification.delivery-type = Delivery Type
push-notification.delivery-timeframe = Delivery Timeframe
push-notification.delivery-timeframe-description = Select the time you want the automated notifications to start being sent to users.
push-notification.trigger-type = Trigger Type
push-notification.ios-badge-number-setting = IOS badge number
push-notification.ios-json-data-setting = IOS JSON data
push-notification.ios-user-data-setting = IOS user data
push-notification.android-badge-number-setting = Android badge number
push-notification.android-json-data-setting = Android JSON data
push-notification.android-user-data-setting = Android user data
internal-events.[CLY]_push_sent = Push sent
internal-events.[CLY]_push_action = Push action
push-notification.android = Android
push-notification.ios = iOS

# Drawer
push-notification.drawer-step-one = Info & Targeting
push-notification.drawer-step-two = Delivery
push-notification.drawer-step-three = Push Content
push-notification.drawer-step-four = Review
push-notification.save = Save
push-notification.send-for-approval = Send for approval
push-notification.save-as-draft = Save as draft
push-notification.create-one-time-notification = Create One-Time Push Notification
push-notification.create-automated-notification = Create Automated Push Notification
push-notification.create-transactional-notification = Create API Push Notification
push-notification.notification-name = Notification Name
push-notification.notification-name-description = Set the name of push notification (optional).
push-notification.enter-notification-name = Enter Notification Name
push-notification.platforms = Platforms
push-notification.android = Android
push-notification.ios = iOS
push-notification.targeting = Targeting
push-notification.targeting-tooltip = Select how to target the users who will recieve the notification.
push-notification.all-push-enabled-users = All push-enabled users
push-notification.all-push-enabled-users-description = Send to all users who have enabled receiving notifications.
push-notification.use-segmentation = Use segmentation
push-notification.use-segmentation-description = Send to users based on specific segmentation such as cohorts or locations.
push-notification.push-enabled-users = Push-enabled users
push-notification.send-to-users-in-cohorts = Send to the users currently in selected cohorts(s)
push-notification.send-to-users-in-cohorts-description = Select cohort(s) of users who qualify to receive the notification.
push-notification.send-to-users-in-locations = Send to the users currently in selected Geolocations
push-notification.send-to-users-in-locations-description = Select geolocation(s) of users to whom you want to send the notification (e.g. users located in France).
push-notification.select-event-to-set-trigger = Select one or more events to set the trigger
push-notification.select-event-to-set-trigger-description = Select one or more events to set the trigger.
push-notification.select-cohort-to-set-trigger = Select one ore more cohorts to set the trigger
push-notification.select-cohort-to-set-trigger-description = Recalculation of cohorts above will trigger sending process automatically.
push-notification.select-location = Please select a location
push-notification.when-to-determine-users = When to determine the users?
push-notification.when-to-determine-users-tooltip = When to determine the number of push-enabled users who will receive the notification.
push-notification.determine-users-before = Determine users right before sending the message
push-notification.determine-users-now = Determine users now
push-notification.triggers = Triggers
push-notification.triggers-description = Select the user behavior that will trigger the message sending process automatically.
push-notification.cohorts-entry = Cohort(s) entry
push-notification.cohorts-entry-description = Triggered when user enters into any of the cohorts you select.
push-notification.cohorts-exit = Cohort(s) exit
push-notification.cohorts-exit-description = Triggered when user exits from any of the cohorts you select.
push-notification.performed-events = Performed Event(s)
push-notification.performed-events-description = Triggered when user performs a selected event.
push-notification.select-event = Please select an event
push-notification.cohorts = Cohort(s)
push-notification.geolocations = Geolocation(s)
push-notification.events = Event(s)
push-notification.delivery-date-calculation = Delivery date calculation
push-notification.behavior-trigger-not-met = Behavior when trigger condition is no longer met
push-notification.start-date = Start date
push-notification.set-start-date = Set Start Date
push-notification.set-start-date-tooltip = Select the time you want the notification to be sent to users.
push-notification.timezone-description = Select whether your push notification needs to adjust for timezone differences.
push-notification.end-date = End date
push-notification.set-end-date = Set End Date
push-notification.set-end-date-tooltip = Select the date when the automatic push notifications will stop being sent.
push-notification.delivery-method = Delivery Method
push-notification.delivery-method-description = Select how soon the push notifications will be sent after the user enters a cohort.
push-notification.capping = Capping
push-notification.capping-tooltip = Select if there will be a limit in the number of automated push notifications sent to a user.
push-notification.no-capping = No Capping
push-notification.no-capping-description = Notification is sent whenever users entered to or exited from cohort.
push-notification.relative-to-the-date-event-server = Relative to the date event arrived to the server
push-notification.relative-to-the-date-event-device = Relative to the date event occurred on a device
push-notification.send-anyway = Send anyway
push-notification.cancel-when-user-exits-cohort = Cancel when user exits selected cohort(s)
push-notification.cancel-when-user-exits-cohort-description = Stops the message from being sent if the user is no longer in the selected cohort(s).
push-notification.send-now = Send now
push-notification.send-now-description = Send the push notification immediately, once composition is complete.
push-notification.scheduled = Scheduled
push-notification.schedule-for-later = Schedule for later
push-notification.delivery-time = Delivery Time
push-notification.delivery-time-tooltip = Set an optional delivery time for your message
push-notification.timezone = Timezone
push-notification.timezone-tooltip = Select whether your push notification needs to adjust for timezone differences.
push-notification.deliver-to-all-users-same-time = Deliver to all users at the same time
push-notification.deliver-to-all-users-same-time-description = Send to all users at a specific time. This may lead to users in different timezones receiving the notification at different hours of the day or night.
push-notification.deliver-to-all-users-device-time = Deliver in user's local time as per device timezone
push-notification.deliver-to-all-users-device-time-description = Send to users at a specific time in their own timezone and on the time settings of the user's device.
push-notification.what-if-past-scheduled = What if the user is past the scheduled time?
push-notification.what-if-past-scheduled-tooltip = Select what happens if a user is past the scheduled time of the push notification.
push-notification.do-not-send-message = Do not send the message
push-notification.deliver-next-day = Deliver the message next day
push-notification.immediately = Immediately
push-notification.immediately-description = Deliver this message as soon as triggering cohort is recalculated.
push-notification.delayed = Delayed
push-notification.days = Days
push-notification.hours = Hours
push-notification.capped = Capped
push-notification.capped-description = Push notifications will no longer be sent when a certain number of notifications is reached.
push-notification.maximum-messages-per-user = Maximum messages per user
push-notification.maximum-messages-per-user-tooltip = Set a limit for the number of automatic push notifications.
push-notification.messages = Messages
push-notification.minimum-time-between-messages = Minimum time between messages
push-notification.minimum-time-between-messages-tooltip = Set the minimum period of time during which notifications will be sent, in order to avoid spamming the user.
push-notification.expiration-time = Expiration Time
push-notification.expiration-time-tooltip = Set the number of days after which push notifications that have been undelivered due to device connectivity issues or other errors will stop retrying to be delivered.
push-notification.notification-type = NOTIFICATION TYPE
push-notification.compose-message = Compose Message
push-notification.compose-message-tooltip = Fill in the details of the message you wish to send to your users.
push-notification.allow-to-set-different-content = Allow to set different message content for different localizations
push-notification.default-message-is-required = Default message is required
push-notification.content-message = Content message
push-notification.silent-message = Silent message
push-notification.message-title = Message title
push-notification.add-variable = Add Variable
push-notification.message-content = Message Content
push-notification.clear-and-shorter-messages = Clear and shorter messages generally have more conversations...
push-notification.buttons = Buttons
push-notification.buttons-tooltip = Set the text for each action button as well as the link or page to which it should lead users.
push-notification.add-first-button = +Add First button
push-notification.add-second-button = +Add Second button
push-notification.enter-x-button = Enter xButton
push-notification.enter-button-url = Enter Button URL or Deeplink
push-notification.media-url = Media URL
push-notification.media-url-description = Add media to your message - put in the URL of the image you would like to include.
push-notification.enter-media-url = Enter Media URL
push-notification.platform-settings = Platform Settings
push-notification.platform-settings-description = Set media and other message specifications specific to the platform on which your users will see the message.
push-notification.sound-file-name = Sound File Name
push-notification.enter-sound-file-name = Enter sound file name
push-notification.sound-file-name-description = Add a custom sound file that will ring with your message.
push-notification.add-badge-number = Add Badge Number
push-notification.enter-badge-number = Enter badge number
push-notification.add-badge-number-description = Android platforms require additional steps. Please refer to Android SDK.
push-notification.media-url-platform-description = Add the URL for media specific to the platform. This will override the media included in the previous drawer.
push-notification.subtitle = Subtitle
push-notification.enter-your-subtitle = Enter your subtitle
push-notification.subtitle-description = Add a subheading for your message.
push-notification.on-click-url = On Click URL
push-notification.enter-on-click-url = Enter on click URL
push-notification.on-click-url-description = Add URL link that is opened when user taps a message in drawer.
push-notification.send-json = Send JSON
push-notification.enter-json-data = Enter JSON data
push-notification.send-json-description = Add app-specific JSON data along with standard content.
push-notification.send-user-data = Send User Data
push-notification.select-user-data = Select user data
push-notification.send-user-data-description = Select user properties to send in your notification payload.
push-notification.icon = Icon
push-notification.icon-description = Set Android notification icon.
push-notification.enter-icon = Enter icon
push-notification.review-message = Review Mesage
push-notification.review-message-tooltip = Review all the details of you push notification.
push-notification.message-name = Message name
push-notification.review-title = Title
push-notification.content = Content
push-notification.button-text = Button Text
push-notification.button-url = Button URL
push-notification.ios-media-url = iOS Media URL
push-notification.android-media-url = Android Media URL
push-notification.ios-badge-number = IOS badge number
push-notification.ios-json-data = iOS JSON data
push-notification.ios-user-data = iOS user data
push-notification.android-badge-number = Android badge number
push-notification.android-json-data = Android JSON data
push-notification.android-user-data = Android user data
push-notification.current-number-of-users = Current number of users
push-notification.when-to-determine = When to determine
push-notification.when-to-determine-description = When to determine the number of push-enabled users who will receive the notification.
push-notification.delivery = Delivery
push-notification.delivery-description = Select the time you want the notification to be sent to users.
push-notification.scheduled-for = Scheduled for
push-notification.message-will-expire-after = Message will expire after {0} days and {1} hours
push-notification.maximum-messages = Maximum {0} messages
push-notification.minimum-days-and-hours = Minimum {0} days and {1} hours between messages
push-notification.confirmation = Confirmation
push-notification.testing = TESTING
push-notification.you-can-send-the-test-message = You can send the test message to test users
push-notification.testing-tooltip = Sends the push notification to applications' test users
push-notification.send-to-test-users = Send to test users
push-notification.confirmation-uppercase = CONFIRMATION
push-notification.confirmation-uppercase-description = CONFIRMATION description
push-notification.i-am-ready-to-send = I am ready to send this message to real-users
push-notification.was-successfully-saved = Push notification message was successfully saved
push-notification.was-successfully-sent-to-test-users = Push notification message was successfully sent to test users
push-notification.was-successfully-approved = Push notification has been successfully approved
push-notification.was-successfully-rejected = Push notification has been successfully rejected
push-notification.was-successfully-deleted = Push notification was successfully deleted
push-notification.was-successfully-started = Push notification was successfully started
push-notification.was-successfully-stopped = Push notification was successfully stopped

# Add user property
push-notification.event-properties = Event Properties
push-notification.user-properties = User Properties
push-notification.custom-properties = Custom Properties
push-notification.add-user-property = Add User Property
push-notification.api-property = Add User Property
push-notification.search-in-properties = Search in Properties
push-notification.select-property = Select Property
push-notification.enter-value = Enter Value
push-notification.start-with-capital-letter = Make user property start with capital letter
push-notification.fallback-value = Fallback value
push-notification.fallback-value-desc = Default value which will be used in case user profile doesn't have this variable
push-notification.remove = Remove
push-notification.confirm = Confirm
push-notification.internal-properties = Internal Properties
push-notification.external-properties = External Properties
push-notification-fallback-value-description = User''s \"{0}\" property which falls back to \"{1}\"


# Details
push-notification-details.back-to = Back to Push Notifications
push-notification-details.localization-filter-label = LOCALIZATION
push-notification-details.localization-filter-all = All Localizations
push-notification-details.summary-header = Notification Summary
push-notification-details.summary-header-description = Overview of the notification message and its details.
push-notification-details.message-tab = Message Content
push-notification-details.targeting-tab = Targeting & Delivery
push-notification-details.errors-tab = Errors
push-notification-details.users-targeted-chart = Users Targeted
push-notification-details.sent-messages-chart = Sent Messages
push-notification-details.and-label = and
push-notification-details.message-id = Message ID
push-notification-details.results-for = Results for
push-notification-details.created = Created
push-notification-details.created-by = Created {0} by {1}

push-notification-details.message-title = Title
push-notification-details.message-content = Content
push-notification-details.message-first-button-label = First Button Text
push-notification-details.message-first-button-url = First Button URL
push-notification-details.message-second-button-label = Second Button Text
push-notification-details.message-second-button-url = Second Button URL
push-notification-details.message-media-url = Media URL
push-notification-details.ios-message-media-url = IOS media URL
push-notification-details.android-message-media-url = Android media URL

push-notification-details.targeting-sub-header = Targeting
push-notification-details.targeted-users = Targeted users
push-notification-details.geolocation = Geolocation
push-notification-details.when-to-determine = When to determine
push-notification-details.delivery-sub-header = Delivery
push-notification-details.delivery-type = Delivery type
push-notification-details.scheduled-for = Scheduled for
push-notification-details.expiration-time = Expiration time
push-notification-details.message-expires-after = Message expires after {0} day(s) and {1} hour(s)
push-notification-details.no-errors-found = No errors were found
push-notification.users-targeted = Users Targeted
push-notification.users-targeted-description = Total number of users targeted to receive the selected notification.
push-notification.sent-notifications = Sent Notifications
push-notification.sent-notifications-description = Total number of notifications sent.
push-notification.clicked-notifications = Clicked Notifications
push-notification.clicked-notifications-description = Total number of notifications clicked on or reacted to.
push-notification.failed = Failed
push-notification.failed-description = Total number of notifications that failed to get delivered.
push-notification.no-errors-found = There are no errors found
push-notification.affected-users = Affected Users
push-notification.error-description = Description
push-notification.error-code = Error Code
push-notification.users = Users
push-notification.back-to-push-notification-details = Back to push notification

# Mobile Preview Component
push-notification.mobile-preview-default-app-name = Your application name
push-notification.mobile-preview-default-title = Your message title
push-notification.mobile-preview-default-content = Your message content

# Application settings
push-notification.ios-settings = iOS settings
push-notification.authentication-type = Authentication type
push-notification.key-file-p8 = Key file (P8)
push-notification.key-file-p12 = Sandbox + Production certificate (P12)
push-notification.key-file-already-uploaded = Key file {0} is already uploaded
push-notification.choose-file = Choose File
push-notification.key-id = Key ID
push-notification.team-id = Team ID
push-notification.bundle-id = Bundle ID
push-notification.passphrase = Passphrase
push-notification.android-settings = Android (Google FCM)
push-notification.firebase-key = Firebase key
push-notification.huawei-settings = Android (Huawei Push Kit)
push-notification.huawei-app-id = App ID
push-notification.huawei-app-secret = App Secret
push-notification.rate-limit = Rate limit
push-notification.maximum-notifications-per-period = Maximum number of notifications scheduled per period
push-notification.period-in-seconds = Period duration (seconds)
push-notification.test-users = Test users
push-notification.test-users-description = Test users description
push-notification.define-new-user = +Define New User
push-notification.user-definition = User Definition
push-notification.user-definition-description = You can send test notifications to users defined as test users before sending push notifications
push-notification.see-user-list = See User List
push-notification.define-new-user-title = Define New User
push-notification.add-test-users-label = Add Users
push-notification.definition-type = Definition Type
push-notification.define-with-user-id = Define with User ID
push-notification.define-with-cohort = Define with Cohort
push-notification.enter-user-id = Enter User ID
push-notification.select-one-or-more-cohorts = Select one or more cohorts to set the trigger
push-notification.select-one-or-more-cohorts-description = Recalculation of cohorts above will trigger sending process automatically.
push-notification.select-cohort = Please select a cohort
push-notification.user-list = User List
push-notification.username = Username
push-notification.user-id = User ID
push-notification.cohort-name = Cohort Name
push-notification.cancel = Cancel
push-notification.i-understand-delete-key = I understand, delete this key
push-notification.delete-key = Delete Key
push-notification.test-users-were-successfully-removed = Test users have been successfully removed
push-notification.test-users-were-successfully-added = Test users have been successfully added

#Global settinsg
push.plugin-title = Push Notifications
push.proxyhost = Proxy Hostname
push.proxypass = Proxy Password
push.proxyport = Proxy Port
push.proxyuser = Proxy Username
push.proxyhttp = Do NOT use HTTPS when connecting to proxy server
push.proxyunauthorized = Do NOT check proxy HTTPS certificate
push.sendahead = Send notifications scheduled up to this many ms into the future
push.connection_retries = Number of connection retries
push.connection_factor = Time factor for exponential backoff between retries
push.pool_pushes = Number of notifications in stream batches
push.pool_bytes = Bytes in binary stream batches
push.pool_concurrency = Maximum number of same type connections
push.pool_pools = Maximum number of connections in total

#Drawer from other views
push-notification.send-message-to-users = Send message to users
push-notification.select-max-two-metrics = Select maximum 2 metrics
# Error codes
push-notification.error-code.400.desc = 400 Bad request
push-notification.error-code.401.desc = 401 Not authorized Please make sure your push notifications credentials are valid.
push-notification.error-code.403.desc = 403 Not authenticated. Please make sure your push notifications credentials are valid.
push-notification.error-code.405.desc = 405 Bad Method
push-notification.error-code.413.desc = 413 Payload too large. Please decrease notification payload size.
push-notification.error-code.429.desc = 429 Too many requests. Please try again later.
push-notification.error-code.500.desc = 500 Server error. Please try again later.
push-notification.error-code.503.desc = 503 Shutdown. Please try again later.
push-notification.error-code.Rejected.desc = The message was in inactive stated during sending.
push-notification.error-code.NoAudience.desc = There're no users to send this notification to. Possibly the filters of this message are too strict.
push-notification.error-code.NoApp.desc = App not found when sending the notification.
push-notification.error-code.NoMessage.desc = The message was not found when sending the notification.
push-notification.error-code.NoCredentials.desc = Push notification credentials were not found when sending the notification.
push-notification.error-code.NoConnection.desc = Failed to connect to push notifications provider, please check APN / FCM / HPK are available from Countly host. In case you use proxy server, please check it's up and running.
push-notification.error-code.NoProxyConnection.desc = Failed to connect to push notifications provider while using proxy server. Please check proxy server settings.
push-notification.error-code.TooLateToSend.desc = Countly was unable to send these notifications in time (1 hour from scheduled date).
push-notification.error-code.ExpiredCreds.desc = Push Notification credentials have probably expired. Please upload new credentials.
push-notification.error-code.BadDeviceToken.desc = The push token received from your app by Countly Server was rejected by APNS as invalid. Please make sure you set `pushTestMode` property on the SDK's initial configuration correctly. Also please make sure provisioning profile (Development or Distribution) is valid, bundle ID is correct and entitlements are properly set.
push-notification.error-code.MissingTopic.desc = The server failed to parse the certificate, please ensure you use universal certificate and contact support if you do.
push-notification.error-code.DeviceTokenNotForTopic.desc = APNS certificate doesn't correspond to the Bundle ID of your application.
push-notification.error-code.TopicDisallowed.desc = Sending Push Notifications to this topic is not allowed. Apple rejects sending Push Notifications to this topic. Most probably there is no such app in Certificates, Identifiers & Profiles portal.
push-notification.error-code.InvalidProviderToken.desc = Please check your Auth key file, Team ID & Bundle ID - some of those is invalid.
push-notification.error-code.MissingRegistration.desc = Please contact customer support.
push-notification.error-code.InvalidRegistration.desc = Probably you modified the way SDK handles FCM tokens. Please ensure you do it right or contact support.
push-notification.error-code.InvalidParameters.desc = Invalid request parameters. Please send server logs to Countly support.
push-notification.error-code.MismatchSenderId.desc = Invalid Sender ID. Most probably SDK competes for tokens with other Firebase SDK you have in your app. <br />Please override the way our SDK or another SDK get a token so they would end up using the same token.
push-notification.error-code.MessageTooBig.desc = Message was too large and Google declined to deliver it.
push-notification.error-code.InvalidDataKey.desc = Message contains invalid data key, please check:
push-notification.error-code.InvalidTtl.desc = Please contact customer support.
push-notification.error-code.DeviceMessageRateExceeded.desc = You send messages to the same device more often than Google allows, please do that less often.
push-notification.error-code.TopicsMessageRateExceeded.desc = You send messages to the same topic more often than Google allows, please do that less often.
push-notification.error-code.Unavailable.desc = Google server unexpectedly returned HTTP error 200 Unavailable. Please try again later.
push-notification.error-code.InternalServerError.desc = Google server unexpectedly returned HTTP error 200 InternalServerError. Please try again later.
push-notification.error-code.NotRegistered.desc = FCM token expired
push-notification.error-code.InvalidRegistration.desc = Probably you modified the way SDK handles FCM tokens. Please ensure you do it right or contact support.
push-notification.error-code.InvalidPackageName.desc = Your application package name doesn't correspond to package name specified in FCM
push-notification.error-code.IllegalToken.desc = Huawei token is expired or invalid
push-notification.error-code.MessageBodyTooBig.desc = Message body is too big for a Huawei notification
push-notification.error-code.TooManyTokens.desc = Too many tokens are being sent to Huawei, please contact Countly support.
push-notification.error-code.NotAuthorizedPriority.desc = You are not authorized to send high-priority Huawei notifications.
push-notification.error-code.InternalHuaweiError.desc = Internal Huawei server error.

push-notification.error-code.TooLateToSend.desc = Countly was unable to send these notifications in time (1 hour from scheduled date).
push-notification.error-code.del = Message deleted
push-notification.error-code.del.desc = Push Notification messages have been discarded due to the message being deleted.
push-notification.error-code.consent = Consent cancelled
push-notification.error-code.consent.desc = Push Notification messages have been removed from the queue because of removed push consent.
push-notification.error-code.purge.desc = Push Notification messages have been removed from the queue because user data has been purged.
push-notification.error-code.aborted = Aborted
push-notification.error-code.aborted.desc = Push Notification messages have been removed from the queue after an nonrecoverable error. Please check our <a target="blank" class="el-link" href="https://support.count.ly/hc/en-us/articles/360037270012-Push-notifications#troubleshooting">Troubleshooting</a> documentation. <br />Please read the following <a target="blank" class="el-link" href="https://support.count.ly/hc/en-us/articles/360037270012#resend-failed-notifications"> instructions </a> if you want to re-send the push notification to users who have not received it.
push-notification.error-code.ExpiredToken = Expired Token
push-notification.error-code.ExpiredToken.desc = The token expired for affected number of users.

# System Logs
systemlogs.action.push_message_created = Push Notification created
systemlogs.action.push_message_draft = Draft Push Notification created
systemlogs.action.push_credentials_update = Push Credentials updated
systemlogs.action.push_message_deleted = Push Notification deleted
systemlogs.action.push_message_updated = Push Notification updated
systemlogs.action.push_message_activated = Push Notification activated
systemlogs.action.push_message_deactivated = Push Notification deactivated
systemlogs.action.push_message_test = Test Push Notification sent
