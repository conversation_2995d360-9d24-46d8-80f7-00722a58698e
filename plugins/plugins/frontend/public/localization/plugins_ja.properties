#plugins

plugins.title = Feature Management
plugins.configs = Settings
plugins.user-configs = Account Settings
plugins.user-account = My Account
plugins.all = All Features
plugins.state = ã¢ã¯ã·ã§ã³
plugins.status = å·
plugins.enabled = æå¹
plugins.enable = æå¹
plugins.disabled = ç¡å¹
plugins.disable = ç¡å¹
plugins.name = Feature name
plugins.description = Description
plugins.version = ãã¼ã¸ã§ã³
plugins.support = ãµãã¼ã
plugins.homepage = ãã¼ã ãã¼ã¸
plugins.wait = æéãããããã¨ãããã¾ãã
plugins.processing = å¤æ´å¦ç
plugins.success = æå
plugins.restart = Countlyãåèµ·åä¸­...
plugins.applying = Applying changes...
plugins.apply = å¤æ´ãé©ç¨ãã
plugins.yes-i-want-to-apply-changes = å¤æ´ãé©ç¨ãã
plugins-apply-changes-to-plugins = ãã©ã°ã¤ã³ã«å¤æ´ãé©ç¨ãã¾ããï¼
plugins.back = æ»ã
plugins.back-to-settings = Back to settings
plugins.search-settings = Search in settings
plugins.hold-on = å°ãå¾æ©
plugins.retry = å¾ã»ã©ååº¦ãã©ã¤ãã¦ãã ããã
plugins.finish = å¤æ´çµäº
plugins.seconds = ç§
plugins.error = ã¨ã©ã¼çºç
plugins.errors = ã¨ã©ã¼çºç
plugins.errors-msg = è©³ç´°æå ±ç¢ºèªã®ãããã­ã°ãç¢ºèªãã¦ãã ããã
plugins.confirm = Disabling  plugin will also disable the functionality that plugin is providing.<br/><br/>Are you sure you want to proceed?
plugins.will-restart = Countly will reload after the changes take effect
plugins.please-wait = Please wait until the process is completed
plugins.dependents = Dependent Features
plugins.disable-descendants = Disabling {0} will cause dependent plugins ({1}) to be disabled as well. Do you want to continue?
plugins.enable-ancestors = Enabling {0} will cause depended plugins ({1}) to be enabled as well. Do you want to continue?
plugins.yes-i-want-to-continue = Yes I want to continue
plugins.indirect-status-change = Indirect status change
plugins.no-access = You do not have any access to any app, contact administrator.

configs.not-changed = Settings not changed
configs.not-saved = è¨­å®ãä¿å­ããã¦ãã¾ããã
configs.changed = Settings changed
configs.saved = å¤æ´ãä¿å­ããã¾ããã
configs.frontend = ãã­ã³ãã¨ã³ã
configs.api = API
configs.regenerate = Regenerate
configs.logger = Incoming Data Logs
configs.apps = ã¢ããªã±ã¼ã·ã§ã³
configs.logs = ã­ã°
configs.security = ã»ã­ã¥ãªãã£
configs.frontend-google_maps_api_key = Google Maps Javascript APIã­ã¼
configs.frontend-production = ãã­ãã¯ã·ã§ã³ã¢ã¼ã
configs.frontend-session_timeout = Session timeout
configs.frontend-theme = ãã¼ã
configs.frontend-use_google = Googleãµã¼ãã¹ä½¿ç¨
configs.no-theme = ããã©ã«ããã¼ã
configs.frontend-code = Show Code Generator for SDK integration
configs.frontend-offline_mode = Offline mode
configs.frontend-countly_tracking = Countly
configs.security-login_tries = è¨±å¯ãããã­ã°ã¤ã³è©¦è¡åæ°
configs.security-login_wait = ä¸æ­£ãªã­ã°ã¤ã³ãã­ãã¯ã®è©¦è¡éé
configs.security-dashboard_additional_headers = è¿½å ã®ããã·ã¥ãã¼ãHTTPã¬ã¹ãã³ã¹ãããã¼
configs.security-robotstxt = Robots.txt
configs.security-proxy_hostname = Proxy Hostname
configs.security-proxy_port = Proxy Port
configs.security-proxy_username = Proxy Username
configs.security-proxy_password = Proxy Password
configs.security-proxy_type = Proxy Type
configs.security-password_min = ãã¹ã¯ã¼ãã®æä½æå­æ°
configs.security-password_char = ãã¹ã¯ã¼ãã«å¤§æå­ãæä½1æå­å«ãã
configs.security-password_number = ãã¹ã¯ã¼ãã«æ°å­ãæä½1ã¤å«ãã
configs.security-password_symbol = ãã¹ã¯ã¼ãã«ç¹æ®æå­ãæä½1ã¤å«ãã
configs.security-password_expiration = ãã¹ã¯ã¼ãã®æå¹æé(æ¥)
configs.security-password_rotation = ãã¹ã¯ã¼ãã®åå©ç¨éé
configs.security-password_autocomplete = Password autocomplete
configs.api-domain = ãµã¼ãã¼URL(éä¿¡ã¡ã¼ã«)
configs.api-safe = å®å¨ãªAPIã¬ã¹ãã³ã¹
configs.api-session_duration_limit = æå¤§ã»ãã·ã§ã³æå¹æé
configs.api-city_data = é½å¸ãã¼ã¿è¿½è·¡
configs.api-country_data = Track country data
configs.api-trim_trailing_ending_spaces= Trim trailing and ending spaces
configs.api-array_list_limit = Max length of array-type properties
configs.api-event_limit = æå¤§åºæã¤ãã³ãã­ã¼
configs.api-event_segmentation_limit = åã¤ãã³ãå¥æå¤§ã»ã°ã¡ã³ãã¼ã·ã§ã³
configs.api-event_segmentation_value_limit = åã»ã°ã¡ã³ãã¼ã·ã§ã³å¥æå¤§åºæå¤
configs.api-sync_plugins = ãã©ã°ã¤ã³ã¹ãã¼ã¿ã¹ã®åæ
configs.api-session_cooldown = ã»ãã·ã§ã³ã®ã¯ã¼ã«ãã¦ã³(ç§)
configs.api-total_users = ãã¹ã¦ã®ã¦ã¼ã¶ã¼
configs.api-metric_limit = ã¡ããªãã¯ã®å¶é
configs.api-metric_changes = ã¡ããªãã¯å¤æ´ãè¨é²
configs.api-request_threshold = é¾å¤ããªã¯ã¨ã¹ã (ç§)
configs.api-export_limit = ãã­ã¥ã¡ã³ãã®ã¨ã¯ã¹ãã¼ãå¶é
configs.api-reports_regenerate_interval = ã¬ãã¼ãç¨åçæéé
configs.api-data_retention_period = Number of days granular data will be retained for
configs.help.api-reports_regenerate_interval = ã¬ãã¼ãåçæã®æå°ééã§ããã¬ãã¼ãã®åçæã«é¸æããæéãããé·ããããå ´åãæ¬¡ã®æãè¿ãééã§åçæããã¾ã
configs.api-prevent_duplicate_requests = éè¤ãªã¯ã¨ã¹ãã®é²æ­¢
configs.api-offline_mode = Offline mode
configs.api-batch_period = Batch write frequency
configs.api-batch_processing = Batch processing
configs.api-user_merge_paralel = Count of lined up merges to be processed in paralel
configs.help.api-user_merge_paralel = Do not increase this number unless server is suffering from long queue of unfinished merges. As more will be processed in paralel it will increase used resources. It is highly recommended to recheck SDK implementation instead of increasing paralel processing.
configs.api-batch_on_master = Batch writes on single process
configs.api-batch_read_processing = Cache reads for SDK API calls
configs.api-batch_read_on_master = Cached reads shared on single processs
configs.api-batch_read_ttl = Cached reads Time To Live
configs.api-batch_read_period = Cached reads update period
configs.security-api_additional_headers = è¿½å ã®HTTPã¬ã¹ãã³ã¹ãããã¼
configs.apps-country = ããã©ã«ãå½
configs.apps-category = ããã©ã«ãã«ãã´ãª
configs.apps-timezone = Application Timezone
configs.account-settings = ã¢ã«ã¦ã³ãè¨­å®
configs.core = ã³ã¢
configs.plugins = ãã©ã°ã¤ã³
configs.search-settings = Search in settings
configs.start-search= Start searching
configs.start-search-description= Type something and press enter to see the results
configs.no-search-result = ...hmm, there are no results
configs.no-search-result-description = Try adjusting your search to find what youâre looking for
configs.user-level-configuration = ã¦ã¼ã¶ã¼ã¬ãã«è¨­å®
configs.table-description = ãã®ã»ã¯ã·ã§ã³ã®è¨­å®ã¯ã¦ã¼ã¶ã¼ã®ã°ã­ã¼ãã«è¨­å®ãä¸æ¸ããã¾ã
configs.security-dashboard_rate_limit_window = ããã·ã¥ãã¼ãçå¶éæé (ç§)
configs.security-dashboard_rate_limit_requests = ããã·ã¥ãã¼ããªã¯ã¨ã¹ãçå¶é
configs.danger-zone = Danger Zone
configs.password = Password
configs.fill-required-fields = Please fill all required fields
configs.delete-account = Delete Account
configs.cannot-be-undone = This action cannot be undone.
configs.will-permanently-delete = This will permanently delete your entire account.
configs.confirm-for-delete = If you want to delete your account, enter your password to confirm.
configs.cancel = Cancel
configs.delete-my-account = Delete my account
configs.change-password = Change password
configs.current-password = Current Password
configs.new-password = New Password
configs.confirmation = Confirm new password
configs.password-specification-1 = Use a password at least 15 letters long or at least
configs.password-specification-2 = 8 characters long with mixed letters and numbers.

configs.api.description = Main API settings
configs.api.batch = Batch processing
configs.api.cache = Cache management
configs.api.limits = Data limits
configs.api.others = Other API settings

configs.overwritten.user = My account settings override this value
configs.overwritten.app = Application settings override this value

configs.logs.default-level = Default Log Level for the rest
configs.logs.modules = Logging for separate features

configs.logs.debug = ãããã°ã¬ãã«
configs.logs.info = æå ±ã¬ãã«
configs.logs.warn = è­¦åã¬ãã«
configs.logs.error = ã¨ã©ã¼ã¬ãã«
configs.logs.default = ããã©ã«ãã¬ãã«
configs.api-send_test_email = Send a test email to me

configs.delete_avatar_failed = Deleting avatar failed

configs.help.api-send_test_email = This will send an email to your email address stored in Countly.
configs.help.api-send_test_email_delivered = Test email successfully sent!
configs.help.api-send_test_email_failed = Sending test e-mail failed!
configs.help.api-send_test_email_subject = Countly test email
configs.help.api-send_test_email_message = Hi,<br/><br/>This is Countly test e-mail and it has been successfully delivered.<br/><br/><br/>Enjoy,<br/>A fellow Countly Admin

configs.help.frontend-offline_mode = When enabled, Countly doesnât connect to Intercom to enable in app chat, Google services to enable Google maps and Countly services to track anonymized service usage.
configs.help.frontend-countly_tracking = When enabled, Countly will be activated on this server to perform server-level analytics and gather user feedback to aid us in continuous product improvement. Personal user data/details or the data you process using this server will never be collected or analyzed. All data is sent exclusively to our dedicated Countly server located in Europe.
configs.help.frontend-production = ããã·ã¥ãã¼ãã®åæèª­è¾¼ã¯ããã¡ã¤ã«ãµã¤ãºãå°ãããã¡ã¤ã«æ°ãå°ãªãããéãã¯ãã§ããããã©ã°ã¤ã³ã®éçºä¸­ã¯ãå¤æ´ãç¢ºèªããããã«ãããã®ãã¡ã¤ã«ãåçæããå¿è¦ãããã¾ãã
configs.help.frontend-theme = é¸æããããã¼ãã¯ãµã¼ãã¼å¨ä½ã«é©ç¨ããããã¹ã¦ã®ã¢ããªã¨ã¦ã¼ã¶ã¼ã«é©ç¨ããã¾ãã
configs.help.frontend-session_timeout = User will be forced to logout after session timeout (in minutes) of inactivity. If you want to disable force logout, set to 0.
configs.help.frontend-google_maps_api_key = Googleã§ã¯æ¦è¦ãåæ > å½ãªã©ã®ãã¥ã¼ã§ä½¿ç¨ãããGeochartãè¡¨ç¤ºããããã®APIã­ã¼ãå¿è¦ã§ãããã®è¡¨ç¤ºãç¡å¶éã§ä½¿ç¨ããããã«ã¯APIã­ã¼ããæä¾ãã ããã<br/><a href="https://developers.google.com/maps/documentation/javascript/get-api-key">APIã­ã¼ãåå¾ããæ¹æ³ã«ã¤ãã¦ã¯ãã¡ãããè¦§ãã ããã</a>
configs.help.security-login_tries = ã¢ã«ã¦ã³ãã¯æå®ã®åæ°ã­ã°ã¤ã³ãééãã¨ä¸å®æéãã­ãã¯ããã¾ããè©¦è¡ééã«ã¤ãã¦ã¯ä¸ã®é ç®ããç¢ºèªãã ããã
configs.help.security-login_wait = ä¸å®ã®åæ°ã­ã°ã¤ã³ã«å¤±æããå¾ã«ã¢ã«ã¦ã³ãããã­ãã¯ãããæé (ç§åä½)
configs.help.security-password_rotation = ã¦ã¼ã¶ã¼ãåå©ç¨ã§ããªãå¤ããã¹ã¯ã¼ãã®æ°
configs.help.security-password_autocomplete = Enable or disable autocomplete on prelogin forms
configs.help.security-robotstxt = Customize to tell search robots what is indexable and what is not
configs.help.security-proxy_hostname = Add your proxy hostname
configs.help.security-proxy_port = Add your proxy port number
configs.help.security-proxy_username = Add your proxy username
configs.help.security-proxy_password = Add your proxy password
configs.help.security-proxy_type = Choose your proxy type
configs.help.api-offline_mode = When enabled, API connections are disabled for email report news, checking new SDKs, pinging external IPs, checking new blogs and making Intercom connections.
configs.help.api-domain = å¤é¨ã¸ã¡ã¼ã«ãéä¿¡ããéã«ä½¿ç¨ããå®å¨ãªãã¡ã¤ã³åã§ãããhttp://SERVERNAMEãã¾ãã¯ãhttps://SERVERNAMEãã®å½¢å¼ã«ããå¿è¦ãããã¾ãã
configs.help.api-safe = æå¹ã«ããã¨ããµã¼ãã¼ã§ã­ã¼ãã©ã¡ã¼ã¿ãæ¤è¨¼ãããã¨ã©ã¼ã¾ãã¯æåã§å¿ç­ãã¾ããããã«ãã£ã¦ãµã¼ãã¼ã®ãªã¼ãã¼ããããå¢ãããããæéã«ä½¿ç¨ãã¦ãã ããã
configs.help.api-session_duration_limit = Maximum session duration value in seconds allowed in any request. If a request contains a session duration higher than this value, it will be capped.
configs.help.api-city_data = Enable tracking city level data in dashboard. If disabled, city information will no longer be added or updated for users.
configs.help.api-country_data = Enable tracking country level data in dashboard. If disabled, country information will no longer be added or updated for users.
configs.help.api-trim_trailing_ending_spaces = If enabled, all trailing and ending spaces will be trimmed from all incoming data.
configs.help.api-event_limit = Maximum number of event keys stored in database. Increasing this number may seriously affect your server performance.
configs.help.api-array_list_limit = If an event segment or custom user property value is an array and its length exceeds the maximum array length set in this setting, only the first n values (where n is the maximum array length) will be retained and any additional values will be discarded.
configs.help.api-event_segmentation_limit = ã«ã¹ã¿ã ã¤ãã³ãæ¯ã®ã»ã°ã¡ã³ãã¼ã·ã§ã³ã®æå¤§æ°ããã®æ°ãå¢ããã¨ãµã¼ãã¼ã®ããã©ã¼ãã³ã¹ã«å½±é¿ãåã¼ãå¯è½æ§ãããã¾ãã
configs.help.api-event_segmentation_value_limit = ã»ã°ã¡ã³ãã¼ã·ã§ã³æ¯ã®åºæå¤ã®æå¤§æ°ããã®æ°ãå¢ããã¨ãµã¼ãã¼ã®ããã©ã¼ãã³ã¹ã«å½±é¿ãåã¼ãå¯è½æ§ãããã¾ãã
configs.help.api-sync_plugins = ãã©ã°ã¤ã³ã®åãæ¿ããããã¨åããã¼ã¿ãã¼ã¹ã®å¨ã¦ã®ãµã¼ãã¼ã«ç¶æãä¼ããã¾ããæå¹ã«ããã¨å¨ãµã¼ãã¼ã«ãã©ã°ã¤ã³ç¶æãä¼ããã¾ããããã©ã°ã¤ã³ç¶æãã§ãã¯å®æ½ã®ãªã¼ãã¼ããããå¢ãã¾ãã
configs.help.api-session_cooldown = ãµã¼ãã¼å´ã«ã¦ãæ°ããã»ãã·ã§ã³ã§ã¯ãªããåã®ã»ãã·ã§ã³ãå»¶é·ããå ´åã®æé
configs.help.api-total_users = æå¹ã«ããã¨ãåè¨ã¦ã¼ã¶ã¼APIãæå¹ã«ããããã¹ã¦ã®ã¬ãã¼ãåã§æ¦ç®ã®åè¨ã¦ã¼ã¶ã¼ã«ã¦ã³ããä¸æ¸ãããã¾ãã ãããæå¹ã«ããã¨ä½åãªãªã¼ãã¼ããããããããããé«è² è·ãªãµã¼ãã¼ã§æå¹ã«ããåã« Countlyã«ãç¸è«ãã ããã
configs.help.api-data_retention_period = Specifies how long to keep granular data before deleting it (0 means never delete it). This setting will only delete granular level data and will not affect aggregated data you see in sections such as Overview and Analytics.
configs.help.api-metric_limit = 1å¹´éæ¯ã®ç°ãªãã¡ããªãã¯ã®å¤ããã®æ°ãå¢ããã¨ãµã¼ãã¼ã®ããã©ã¼ãã³ã¹ã«å½±é¿ãåã¼ãå¯è½æ§ãããã¾ãã
configs.help.api-metric_changes = åè¨ã¦ã¼ã¶ã¼ä¿®æ­£ãä½¿ç¨ããå ´åãå¤æ´ã®è¨é²ã¯å¿è¦ã§ããåè¨ã¦ã¼ã¶ã¼ä¿®æ­£ãä½¿ç¨ããªãå ´åã¯ç¡å¹ã«ãã¦ãã ããã
configs.help.api-request_threshold = ããªã«ããã¡ãã«ãä»ã®é¡ä¼¼ã®ããã·ã¥ãã¼ãæ©è½ãªã¯ã¨ã¹ãã®å®äºã«æéããããå ´åã«ã¬ãã¼ãããã¼ã¸ã£ã¼ã«åãæ¿ããã¾ã§ã®æéãHTTPã¿ã¤ã ã¢ã¦ã (ããã©ã«ãã¯60ç§) ãããé·ãæéã«è¨­å®ããªãã§ãã ãããã¬ã¹ãã³ã¹ãåãåããªããªãã¾ãã
configs.help.api-export_limit = 1ã¤ã®ãã¡ã¤ã«ã«ã¨ã¯ã¹ãã¼ããããCSVãPDFåã¯CSVãã­ã¥ã¡ã³ãã®è¡æ° 
configs.help.api-prevent_duplicate_requests = ãªã¯ã¨ã¹ãã®éè¤ãé²ãããã«ãªã¯ã¨ã¹ãããã·ã¥ãä¿å­ãã¦æ¯è¼ãã¾ã
configs.help.api-batch_period = How often to commit batch writes to database (in seconds)
configs.help.api-batch_processing = Combine aggregated data writes together and commit to database with specific frequency
configs.help.api-batch_on_master = If enabled, all processes will send data to single process to commit to database. Helps if you have many CPU cores per instance
configs.help.api-batch_read_processing = Some reads that happen on each SDK call can be cached for reusing
configs.help.api-batch_read_on_master = This will share all cached reads on single proces instead of each process having their own cache. Helps if you have many CPU cores per instance
configs.help.api-batch_read_ttl = How long should cache be kept if unused (in seconds)
configs.help.api-batch_read_period = How often should cache be updated (in seconds)
configs.help.apps-country = APIãä½¿ç¨ãã¦å½æå®ãªãã®ã¢ããªãçæããéã«ä½¿ç¨ãããããã©ã«ãã®å½
configs.help.apps-category = APIãä½¿ç¨ãã¦ã«ãã´ãªãªãã®ã¢ããªãçæä½æããéã«ä½¿ç¨ãããããã©ã«ãã®ã«ãã´ãª
configs.help.logs-debug = ã¢ããªã±ã¼ã·ã§ã³ããããã°ããããã«æãæç¨ãªæå ±ã¤ãã³ããè¨é²
configs.help.logs-info = ã¢ããªã±ã¼ã·ã§ã³ã®é²è¡ç¶æ³ãè¡¨ç¤ºããæå ±ã¡ãã»ã¼ã¸ãè¨é²
configs.help.logs-warn = å±éºãªå¯è½æ§ã®ããç¶æ³ãã­ã°ã«è¨é²
configs.help.logs-error = ã¢ããªã±ã¼ã·ã§ã³ã®åä½ãç¶è¡ã§ããå¯è½æ§ãããã¨ã©ã¼ã¤ãã³ããã­ã°ã«è¨é²
configs.help.logs-default = /logãã£ã¬ã¯ããªã®ã­ã°ãã¡ã¤ã«ã«åºåãããåã¢ã¸ã¥ã¼ã«å¥ãããã°ã¬ãã«
configs.help.crashes-report_limit = ã¯ã©ãã·ã¥ã°ã«ã¼ããã¼ã¸æ¯ã«è¡¨ç¤ºããã¬ãã¼ãæ°
configs.help.frontend-use_google = Googleãµã¼ãã¹ããã­ãã¯ããã¦ããå½ã«ãä½ãã®å ´åã¯ãã®ãªãã·ã§ã³ãç¡å¹ã«ãã¦ãã ãããç¡å¹ã«ããå ´åãç¹å®ã®Googleãµã¼ãã¹ï¼ããããªã©ï¼ã¯è¡¨ç¤ºããã¾ããã
configs.help.frontend-code = æ°è¦ã¢ããªä½æãã¼ã¸ä¸ã®Countly Codeã¸ã§ãã¬ã¼ã¿ã¼ã®ãªã³ã¯ãè¡¨ç¤ºã
configs.help.security-dashboard_additional_headers = Countlyããã·ã¥ãã¼ãåãã®ãããã¼ãè¿½å (1ã¤ã®è¡ã«ã¤ãã1ã¤ã®ãããã¼)
configs.help.security-api_additional_headers = Countly APIåãã®ãããã¼ãè¿½å (1ã¤ã®è¡ã«ã¤ãã1ã¤ã®ãããã¼)
configs.help.security-password_min = ãã¹ã¯ã¼ãã®æä½æå­æ°
configs.help.security-password_char = æå¹ã«ããã¨ãè¨­å®ãããã¹ã¯ã¼ãã«å¤§æå­ãæä½1æå­å«ããå¿è¦ãããã¾ãã
configs.help.security-password_number = æå¹ã«ããã¨ãè¨­å®ãããã¹ã¯ã¼ãã«ã¯0ï½9ã®æ°å­ãæä½1ã¤å«ã¾ãã¦ããå¿è¦ãããã¾ã
configs.help.security-password_symbol = æå¹ã«ããã¨ãè¨­å®ãããã¹ã¯ã¼ãã«ã¯æ°å­ãã¢ã«ãã¡ãããä»¥å¤ã®ç¹æ®æå­ãæä½1ã¤å«ããå¿è¦ãããã¾ã
configs.help.security-password_expiration = ãã¹ã¯ã¼ããªã»ãããè¡ãããå¾ã®æ¥æ°
configs.help.user-level-configuration = å¥ãã®ããã·ã¥ãã¼ãã®ã¦ã¼ã¶ã¼ãèªåãã¡ã®ã¢ã«ã¦ã³ãã®ã¿ã«å¯¾ãã¦ãããã®è¨­å®ãå¤æ´ã§ããããã«ãã¾ãã
configs.help.security-dashboard_rate_limit_window = ãªã¯ã¨ã¹ãæ°ããã®æéæ ã«éããå ´åã«ãã­ãã¯éå§
configs.help.security-dashboard_rate_limit_requests = åæéæ ã§è¨±å¯ãããªã¯ã¨ã¹ãæ°
configs.help.push-proxyhost = ããã·ã¥éç¥ãéä¿¡ããéã«APN & FCMã§ã®éä¿¡ã«ä½¿ç¨ããããã®HTTP CONNECTãã­ã­ã·ãµã¼ãã¼ã®ãã¹ãååã¯IPã¢ãã¬ã¹ã
configs.help.push-proxyport = ãã­ã­ã·ãµã¼ãã¼ã®ãã¼ãçªå·
configs.help.push-proxyuser = (å¿è¦ã«å¿ãã¦) ãã­ã­ã·ã¼ãµã¼ãã¼HTTPåºæ¬èªè¨¼ã®ã¦ã¼ã¶ã¼å
configs.help.push-proxypass = (å¿è¦ã«å¿ãã¦) ãã­ã­ã·ã¼ãµã¼ãã¼HTTPåºæ¬èªè¨¼ã®åãã¹ã¯ã¼ã
configs.help.push-proxyunauthorized = (if needed) Allow self signed certificates without CA installed

systemlogs.action.change_configs = Setting Changed
systemlogs.action.change_plugins = ãã©ã°ã¤ã³ãæ´æ°ãã¾ãã
