#plugins

plugins.title = Feature Management
plugins.configs = Settings
plugins.user-configs = Account Settings
plugins.user-account = My Account
plugins.all = All Features
plugins.state = TevÃ©kenysÃ©g
plugins.status = Ãllam
plugins.enabled = EngedÃ©lyezett
plugins.enable = EngedÃ©lyezÃ©s
plugins.disabled = Letiltott
plugins.disable = LetiltÃ¡s
plugins.name = Feature name
plugins.description = Description
plugins.version = VerziÃ³
plugins.support = Support
plugins.homepage = FÅoldal
plugins.wait = Ez egy kis idÅt igÃ©nybevehet
plugins.processing = VÃ¡ltoztatÃ¡sok folyamatban
plugins.success = Sikeres
plugins.restart = Restarting Countly...
plugins.applying = Applying changes...
plugins.apply = VÃ¡ltoztatÃ¡sok vÃ©glegesÃ­tÃ©se
plugins.yes-i-want-to-apply-changes = Yes I want to apply changes
plugins-apply-changes-to-plugins = Apply changes to plugins?
plugins.back = Vissza
plugins.back-to-settings = Back to settings
plugins.search-settings = Search in settings
plugins.hold-on = FolytatÃ¡s
plugins.retry = PrÃ³bÃ¡ld Ãºjra kÃ©sÅbb
plugins.finish = a vÃ¡ltoztatÃ¡sok befejezÃ©sÃ©hez
plugins.seconds = mÃ¡sodperc
plugins.error = Hiba tÃ¶rtÃ©nt
plugins.errors = NÃ©hÃ¡ny hiba tÃ¶rtÃ©nt
plugins.errors-msg = EllenÅrizd a naplÃ³t a tovÃ¡bbi informÃ¡ciÃ³kÃ©rt
plugins.confirm = Disabling  plugin will also disable the functionality that plugin is providing.<br/><br/>Are you sure you want to proceed?
plugins.will-restart = Countly will reload after the changes take effect
plugins.please-wait = Please wait until the process is completed
plugins.dependents = Dependent Features
plugins.disable-descendants = Disabling {0} will cause dependent plugins ({1}) to be disabled as well. Do you want to continue?
plugins.enable-ancestors = Enabling {0} will cause depended plugins ({1}) to be enabled as well. Do you want to continue?
plugins.yes-i-want-to-continue = Yes I want to continue
plugins.indirect-status-change = Indirect status change
plugins.no-access = You do not have any access to any app, contact administrator.

configs.not-changed = Settings not changed
configs.not-saved = Nem sikerÃ¼lt a vÃ¡ltoztatÃ¡sok elmentÃ©se
configs.changed = Settings changed
configs.saved = VÃ¡ltoztatÃ¡sok elmentÃ©se sikeresen megtÃ¶rtÃ©nt
configs.frontend = Frontend
configs.api = API
configs.regenerate = Regenerate
configs.logger = Incoming Data Logs
configs.apps = ApplikÃ¡ciÃ³k
configs.logs = NaplÃ³
configs.security = BiztonsÃ¡g
configs.frontend-google_maps_api_key = Google Maps Javascript API key
configs.frontend-production = TermelÃ©si mÃ³d
configs.frontend-session_timeout = Session timeout
configs.frontend-theme = TÃ©ma
configs.frontend-use_google = Google Service hasznÃ¡lata
configs.no-theme = AlapÃ©rtelmezett tÃ©ma
configs.frontend-code = Show Code Generator for SDK integration
configs.frontend-offline_mode = Offline mode
configs.frontend-countly_tracking = Countly
configs.security-login_tries = Allowed login attempts
configs.security-login_wait = Incorrect login block time increment
configs.security-dashboard_additional_headers = TovÃ¡bbi MÅ±szerfal HTTP vÃ¡lasz fejlÃ©cek
configs.security-robotstxt = Robots.txt
configs.security-proxy_hostname = Proxy Hostname
configs.security-proxy_port = Proxy Port
configs.security-proxy_username = Proxy Username
configs.security-proxy_password = Proxy Password
configs.security-proxy_type = Proxy Type
configs.security-password_min = Minimum password length
configs.security-password_char = A jelszÃ³nak egy nagybetÅ±t tartalmaznia kell
configs.security-password_number = A jelszÃ³nak legalÃ¡bb egy szÃ¡mot tartalmaznia kell
configs.security-password_symbol = A jelszÃ³nak legalÃ¡bb egy speciÃ¡lis szimbÃ³lumot tartalmaznia kell
configs.security-password_expiration = JelszÃ³ Ã©rvÃ©nyessÃ©g (napokban)
configs.security-password_rotation = JelszÃ³ rotÃ¡lÃ¡s
configs.security-password_autocomplete = Password autocomplete
configs.api-domain = Szerver URL(felhasznÃ¡lva kimenÅ emailekhez)
configs.api-safe = BiztonsÃ¡gosabb API vÃ¡laszok
configs.api-session_duration_limit = Legnagyobb munkamenet idÅtartam
configs.api-city_data = VÃ¡ros adatok kÃ¶vetÃ©se
configs.api-country_data = Track country data
configs.api-trim_trailing_ending_spaces= Trim trailing and ending spaces
configs.api-array_list_limit = Max length of array-type properties
configs.api-event_limit = Max egyedi esemÃ©ny kulcs
configs.api-event_segmentation_limit = EsemÃ©nyenkÃ©nti maximÃ¡lis szegmentÃ¡lÃ¡s
configs.api-event_segmentation_value_limit = SzegmensenkÃ©nti maximÃ¡lis egyedi Ã©rtÃ©k
configs.api-sync_plugins = Plugin stÃ¡tusz szinkronizÃ¡lÃ¡sa
configs.api-session_cooldown = Munkamenet lezajlÃ¡s (mÃ¡sodpercben)
configs.api-total_users = Ãsszes felhasznÃ¡lÃ³
configs.api-metric_limit = MÃ©rÅszÃ¡m limit
configs.api-metric_changes = Record metric changes
configs.api-request_threshold = LekÃ©rÃ©s kÃ¼szÃ¶b (mÃ¡sodperc)
configs.api-export_limit = Document export limit
configs.api-reports_regenerate_interval = Regeneration interval for reports
configs.api-data_retention_period = Number of days granular data will be retained for
configs.help.api-reports_regenerate_interval = Minimum report regeneration interval. If a report regeneration takes longer than the selected duration, itâll be regenerated in the next closest interval
configs.api-prevent_duplicate_requests = Prevent duplicate requests
configs.api-offline_mode = Offline mode
configs.api-batch_period = Batch write frequency
configs.api-batch_processing = Batch processing
configs.api-user_merge_paralel = Count of lined up merges to be processed in paralel
configs.help.api-user_merge_paralel = Do not increase this number unless server is suffering from long queue of unfinished merges. As more will be processed in paralel it will increase used resources. It is highly recommended to recheck SDK implementation instead of increasing paralel processing.
configs.api-batch_on_master = Batch writes on single process
configs.api-batch_read_processing = Cache reads for SDK API calls
configs.api-batch_read_on_master = Cached reads shared on single processs
configs.api-batch_read_ttl = Cached reads Time To Live
configs.api-batch_read_period = Cached reads update period
configs.security-api_additional_headers = TovÃ¡bbi API HTTP vÃ¡lasz fejlÃ©cek
configs.apps-country = AlapÃ©rtelmezett OrszÃ¡g
configs.apps-category = AlapÃ©rtelmezett kategÃ³ria
configs.apps-timezone = Application Timezone
configs.account-settings = FiÃ³k beÃ¡llÃ­tÃ¡sok
configs.core = Core
configs.plugins = Plugins
configs.search-settings = Search in settings
configs.start-search= Start searching
configs.start-search-description= Type something and press enter to see the results
configs.no-search-result = ...hmm, there are no results
configs.no-search-result-description = Try adjusting your search to find what youâre looking for
configs.user-level-configuration = User Level Configuration
configs.table-description = Settings in this section will override global settings for the user
configs.security-dashboard_rate_limit_window = Dashboard Rate Limit Time (seconds)
configs.security-dashboard_rate_limit_requests = Dashboard Request Rate Limit
configs.danger-zone = Danger Zone
configs.password = Password
configs.fill-required-fields = Please fill all required fields
configs.delete-account = Delete Account
configs.cannot-be-undone = This action cannot be undone.
configs.will-permanently-delete = This will permanently delete your entire account.
configs.confirm-for-delete = If you want to delete your account, enter your password to confirm.
configs.cancel = Cancel
configs.delete-my-account = Delete my account
configs.change-password = Change password
configs.current-password = Current Password
configs.new-password = New Password
configs.confirmation = Confirm new password
configs.password-specification-1 = Use a password at least 15 letters long or at least
configs.password-specification-2 = 8 characters long with mixed letters and numbers.

configs.api.description = Main API settings
configs.api.batch = Batch processing
configs.api.cache = Cache management
configs.api.limits = Data limits
configs.api.others = Other API settings

configs.overwritten.user = My account settings override this value
configs.overwritten.app = Application settings override this value

configs.logs.default-level = Default Log Level for the rest
configs.logs.modules = Logging for separate features

configs.logs.debug = HibakeresÃ©si szint
configs.logs.info = InformÃ¡ciÃ³s szint
configs.logs.warn = FigyelmeztetÃ©si szint
configs.logs.error = Hiba szint
configs.logs.default = AlapÃ©rtelmezett szint
configs.api-send_test_email = Send a test email to me

configs.delete_avatar_failed = Deleting avatar failed

configs.help.api-send_test_email = This will send an email to your email address stored in Countly.
configs.help.api-send_test_email_delivered = Test email successfully sent!
configs.help.api-send_test_email_failed = Sending test e-mail failed!
configs.help.api-send_test_email_subject = Countly test email
configs.help.api-send_test_email_message = Hi,<br/><br/>This is Countly test e-mail and it has been successfully delivered.<br/><br/><br/>Enjoy,<br/>A fellow Countly Admin

configs.help.frontend-offline_mode = When enabled, Countly doesnât connect to Intercom to enable in app chat, Google services to enable Google maps and Countly services to track anonymized service usage.
configs.help.frontend-countly_tracking = When enabled, Countly will be activated on this server to perform server-level analytics and gather user feedback to aid us in continuous product improvement. Personal user data/details or the data you process using this server will never be collected or analyzed. All data is sent exclusively to our dedicated Countly server located in Europe.
configs.help.frontend-production = Initial load of dashboard should be faster, due to smaller files and smaller file amount, but when developing a plugin, you need to regenerate them to see changes
configs.help.frontend-theme = A kivÃ¡lasztott tÃ©ma szerver szinten elÃ©rhetÅ az Ã¶sszes applikÃ¡ciÃ³nak Ã©s felhasznÃ¡lÃ³nak
configs.help.frontend-session_timeout = User will be forced to logout after session timeout (in minutes) of inactivity. If you want to disable force logout, set to 0.
configs.help.frontend-google_maps_api_key = Google requires an API key for Geocharts visualization used in views such as Overview and Analytics > Countries. Provide your API key to use this visualization without any limitations.<br/><a href="https://developers.google.com/maps/documentation/javascript/get-api-key">Learn how to get your API key.</a>
configs.help.security-login_tries = Account will be blocked for some time after provided number of incorrect login attempts. See below for time increments.
configs.help.security-login_wait = Incremental period of time account is blocked after provided number of incorrect login attempts (in seconds)
configs.help.security-password_rotation = Azon felhasznÃ¡lÃ³k szÃ¡ma amiket a a felhasznÃ¡lÃ³ nem hasznÃ¡lhat ÃºjbÃ³l
configs.help.security-password_autocomplete = Enable or disable autocomplete on prelogin forms
configs.help.security-robotstxt = Customize to tell search robots what is indexable and what is not
configs.help.security-proxy_hostname = Add your proxy hostname
configs.help.security-proxy_port = Add your proxy port number
configs.help.security-proxy_username = Add your proxy username
configs.help.security-proxy_password = Add your proxy password
configs.help.security-proxy_type = Choose your proxy type
configs.help.api-offline_mode = When enabled, API connections are disabled for email report news, checking new SDKs, pinging external IPs, checking new blogs and making Intercom connections.
configs.help.api-domain = This is the full qualified domain name used in outgoing emails. It should be in the form of http://SERVERNAME or https://SERVERNAME
configs.help.api-safe = If enabled, server will verify key parameters and respond with error or success. This increases server overhead, so use with care.
configs.help.api-session_duration_limit = Maximum session duration value in seconds allowed in any request. If a request contains a session duration higher than this value, it will be capped.
configs.help.api-city_data = Enable tracking city level data in dashboard. If disabled, city information will no longer be added or updated for users.
configs.help.api-country_data = Enable tracking country level data in dashboard. If disabled, country information will no longer be added or updated for users.
configs.help.api-trim_trailing_ending_spaces = If enabled, all trailing and ending spaces will be trimmed from all incoming data.
configs.help.api-event_limit = Maximum number of event keys stored in database. Increasing this number may seriously affect your server performance.
configs.help.api-array_list_limit = If an event segment or custom user property value is an array and its length exceeds the maximum array length set in this setting, only the first n values (where n is the maximum array length) will be retained and any additional values will be discarded.
configs.help.api-event_segmentation_limit = Maximum number of segmentations per custom events. Increasing this number may affect your server performance.
configs.help.api-event_segmentation_value_limit = Maximum number of unique values in each segmentation. Increasing this number may affect your server performance.
configs.help.api-sync_plugins = Toggling plugin will propagate state to all servers with same database. Enabling would propagate plugin state to all server, but also have more overhead performing plugin state checks.
configs.help.api-session_cooldown = Az idÅ a munkamenet vÃ©ge Ã©s eleje kÃ¶zÃ¶tt, amikor a szerver meghosszabbÃ­tja az elÅzÅ munkamenetet az Ãºj helyett
configs.help.api-total_users = If enabled, total users API will be enabled and used to override estimated total user counts in all reports. Enabling this will provide extra overhead, so consult Countly before enabling for highly loaded servers.
configs.help.api-data_retention_period = Specifies how long to keep granular data before deleting it (0 means never delete it). This setting will only delete granular level data and will not affect aggregated data you see in sections such as Overview and Analytics.
configs.help.api-metric_limit = Number of different metric values per annual period. Increasing this number may affect your server performance.
configs.help.api-metric_changes = Recording changes is required by Total users correction. Disable it if you know you won't be using Total users correction
configs.help.api-request_threshold = Time before switching to report manager if a drill, funnel or other similar dashboard functionality request takes too long to complete. This should not be longer than HTTP timeout, which by default is 60 seconds or you won't get any response.
configs.help.api-export_limit = Amount of lines in a CSV, PDF or CSV document exported in single file
configs.help.api-prevent_duplicate_requests = Stores and compares request hash to prevent duplicate requests
configs.help.api-batch_period = How often to commit batch writes to database (in seconds)
configs.help.api-batch_processing = Combine aggregated data writes together and commit to database with specific frequency
configs.help.api-batch_on_master = If enabled, all processes will send data to single process to commit to database. Helps if you have many CPU cores per instance
configs.help.api-batch_read_processing = Some reads that happen on each SDK call can be cached for reusing
configs.help.api-batch_read_on_master = This will share all cached reads on single proces instead of each process having their own cache. Helps if you have many CPU cores per instance
configs.help.api-batch_read_ttl = How long should cache be kept if unused (in seconds)
configs.help.api-batch_read_period = How often should cache be updated (in seconds)
configs.help.apps-country = Alap orszÃ¡g beÃ¡llÃ­tÃ¡s, ha az alkalmazÃ¡s letrehozÃ¡sa sorÃ¡n van bekÃ¶tve API, de nincs kivÃ¡lasztva orszÃ¡g
configs.help.apps-category = Alap kategÃ³ria beÃ¡llÃ­tÃ¡s, ha az alkalmazÃ¡s letrehozÃ¡sa sorÃ¡n van bekÃ¶tve API, de nincs kivÃ¡lasztva kategÃ³ria
configs.help.logs-debug = NaplÃ³zz rÃ©szletes informÃ¡ciÃ³s esemÃ©nyeket, hogy minÃ©l kÃ¶nnyebben vÃ©gezhess hibakeresÃ©st az alkalmazÃ¡sodban
configs.help.logs-info = NaplÃ³zz informÃ¡ciÃ³s Ã¼zeneteket amik kiemelik az alkalmazÃ¡s elÅmenetelÃ©t
configs.help.logs-warn = NaplÃ³zz potenciÃ¡lisan kÃ¡ros szituÃ¡ciÃ³kat!
configs.help.logs-error = NaplÃ³ hiba esemÃ©nyek, amik tovÃ¡bbra is engedÃ©lyezhetik az alkalmazÃ¡s futÃ¡sÃ¡t
configs.help.logs-default = Minden modul hibakeresÃ©si informÃ¡ciÃ³s szintje alapÃ©rtelmezetten mutasson a /log kÃ¶nyvtÃ¡rba.
configs.help.crashes-report_limit = Az Ã¶sszeomlÃ¡s csoport oldalakban megjelenÃ­tett riportok szÃ¡ma
configs.help.frontend-use_google = Disable this option, if you are in the country where Google services are blocked. If disabled, certain Google services (e.g maps) wonât be visible.
configs.help.frontend-code = Display links for Countly Code generator under new app creation page.
configs.help.security-dashboard_additional_headers = Adj a Countlyhoz fejlÃ©cet, hogy hasznÃ¡lhasd a MÅ±szerfalon (1 fejlÃ©c soronkÃ©nt)
configs.help.security-api_additional_headers = Adj a Countlyhoz fejlÃ©cet, hogy hasznÃ¡lhasd az API vÃ¡laszoknÃ¡l (1 fejlÃ©c soronkÃ©nt)
configs.help.security-password_min = Minimum number of characters used in the password
configs.help.security-password_char = If enabled, provided passwords must contain at least one uppercase character.
configs.help.security-password_number = If enabled, provided passwords must contain at least one digit (e.g 0..9)
configs.help.security-password_symbol = If enabled, provided passwords must contain at least one special symbol (not a number or latin character)
configs.help.security-password_expiration = Azon napok szÃ¡ma, amelyek utÃ¡n a felhasznÃ¡lÃ³nak vissza kell Ã¡llÃ­tania a jelszÃ³t
configs.help.user-level-configuration = Allow separate dashboard users to change these configs for their account only.
configs.help.security-dashboard_rate_limit_window = Will start blocking if request amount is reached in this time window
configs.help.security-dashboard_rate_limit_requests = How many requests to allow per time window?
configs.help.push-proxyhost = Hostname or IP address of HTTP CONNECT proxy server to use for communication with APN & FCM when sending push notifications.
configs.help.push-proxyport = Port number of the proxy server
configs.help.push-proxyuser = (if needed) Username for proxy server HTTP Basic authentication
configs.help.push-proxypass = (if needed) Password for proxy server HTTP Basic authentication
configs.help.push-proxyunauthorized = (if needed) Allow self signed certificates without CA installed

systemlogs.action.change_configs = Setting Changed
systemlogs.action.change_plugins = Pluginok megvÃ¡ltoztak
