#plugins

plugins.title = Feature Management
plugins.configs = Settings
plugins.user-configs = Account Settings
plugins.user-account = My Account
plugins.all = All Features
plugins.state = Action
plugins.status = State
plugins.enabled = Enabled
plugins.enable = Enable
plugins.disabled = Disabled
plugins.disable = Disable
plugins.name = Feature name
plugins.description = Description
plugins.version = Version
plugins.support = Support
plugins.homepage = Homepage
plugins.wait = This might take a while
plugins.processing = Processing changes
plugins.success = Success
plugins.restart = Restarting Countly...
plugins.applying = Applying changes...
plugins.apply = Apply Changes
plugins.yes-i-want-to-apply-changes = Yes I want to apply changes
plugins-apply-changes-to-plugins = Apply changes to plugins?
plugins.back = Back
plugins.back-to-settings = Back to settings
plugins.search-settings = Search in settings
plugins.hold-on = Hold on
plugins.retry = Try again later
plugins.finish = to finish changes
plugins.seconds = seconds
plugins.error = Error occurred
plugins.errors = Some errors occurred
plugins.errors-msg = Check logs for more information
plugins.confirm = Disabling  plugin will also disable the functionality that plugin is providing.<br/><br/>Are you sure you want to proceed?
plugins.will-restart = Countly will reload after the changes take effect
plugins.please-wait = Please wait until the process is completed
plugins.dependents = Dependent Features
plugins.disable-descendants = Disabling {0} will cause dependent plugins ({1}) to be disabled as well. Do you want to continue?
plugins.enable-ancestors = Enabling {0} will cause depended plugins ({1}) to be enabled as well. Do you want to continue?
plugins.yes-i-want-to-continue = Yes I want to continue
plugins.indirect-status-change = Indirect status change
plugins.no-access = You do not have any access to any app, contact administrator.

configs.not-changed = Settings not changed
configs.not-saved = Could not save changes
configs.changed = Settings changed
configs.saved = Changes were successfully saved
configs.frontend = Frontend
configs.api = API
configs.regenerate = Regenerate
configs.logger = Incoming Data Logs
configs.apps = Apps
configs.logs = Logs
configs.security = Security
configs.frontend-google_maps_api_key = Google Maps Javascript API key
configs.frontend-production = Production mode
configs.frontend-session_timeout = Session timeout
configs.frontend-theme = Theme
configs.frontend-use_google = Use Google Services
configs.no-theme = Default Theme
configs.frontend-code = Show Code Generator for SDK integration
configs.frontend-offline_mode = Offline mode
configs.frontend-countly_tracking = Countly
configs.security-login_tries = Allowed login attempts
configs.security-login_wait = Incorrect login block time increment
configs.security-dashboard_additional_headers = Additional Dashboard HTTP Response headers
configs.security-robotstxt = Robots.txt
configs.security-proxy_hostname = Proxy Hostname
configs.security-proxy_port = Proxy Port
configs.security-proxy_username = Proxy Username
configs.security-proxy_password = Proxy Password
configs.security-proxy_type = Proxy Type
configs.security-password_min = Minimum password length
configs.security-password_char = Password must contain an uppercase character
configs.security-password_number = Password must contain a number
configs.security-password_symbol = Password must contain a special symbol
configs.security-password_expiration = Password expiration (in days)
configs.security-password_rotation = Password rotation
configs.security-password_autocomplete = Password autocomplete
configs.api-domain = Server URL (used in outgoing emails)
configs.api-safe = Safer API responses
configs.api-session_duration_limit = Maximal Session Duration
configs.api-city_data = Track city data
configs.api-country_data = Track country data
configs.api-trim_trailing_ending_spaces= Trim trailing and ending spaces
configs.api-array_list_limit = Max length of array-type properties
configs.api-event_limit = Max unique event keys
configs.api-event_segmentation_limit = Max segmentation in each event
configs.api-event_segmentation_value_limit = Max unique values in each segmentation
configs.api-sync_plugins = Sync plugin states
configs.api-session_cooldown = Session cooldown (seconds)
configs.api-total_users = Total users
configs.api-metric_limit = Metric limit
configs.api-metric_changes = Record metric changes
configs.api-request_threshold = Request threshold (seconds)
configs.api-export_limit = Document export limit
configs.api-reports_regenerate_interval = Regeneration interval for reports
configs.api-data_retention_period = Number of days granular data will be retained for
configs.help.api-reports_regenerate_interval = Minimum report regeneration interval. If a report regeneration takes longer than the selected duration, itâll be regenerated in the next closest interval
configs.api-prevent_duplicate_requests = Prevent duplicate requests
configs.api-offline_mode = Offline mode
configs.api-batch_period = Batch write frequency
configs.api-batch_processing = Batch processing
configs.api-user_merge_paralel = Count of lined up merges to be processed in paralel
configs.help.api-user_merge_paralel = Do not increase this number unless server is suffering from long queue of unfinished merges. As more will be processed in paralel it will increase used resources. It is highly recommended to recheck SDK implementation instead of increasing paralel processing.
configs.api-batch_on_master = Batch writes on single process
configs.api-batch_read_processing = Cache reads for SDK API calls
configs.api-batch_read_on_master = Cached reads shared on single processs
configs.api-batch_read_ttl = Cached reads Time To Live
configs.api-batch_read_period = Cached reads update period
configs.security-api_additional_headers = Additional API HTTP Response headers
configs.apps-country = Default Country
configs.apps-category = Default Category
configs.apps-timezone = Application Timezone
configs.account-settings = Account Settings
configs.core = Core
configs.plugins = Plugins
configs.search-settings = Search in settings
configs.start-search= Start searching
configs.start-search-description= Type something and press enter to see the results
configs.no-search-result = ...hmm, there are no results
configs.no-search-result-description = Try adjusting your search to find what youâre looking for
configs.user-level-configuration = User Level Configuration
configs.table-description = Settings in this section will override global settings for the user
configs.security-dashboard_rate_limit_window = Dashboard Rate Limit Time (seconds)
configs.security-dashboard_rate_limit_requests = Dashboard Request Rate Limit
configs.danger-zone = Danger Zone
configs.password = Password
configs.fill-required-fields = Please fill all required fields
configs.delete-account = Delete Account
configs.cannot-be-undone = This action cannot be undone.
configs.will-permanently-delete = This will permanently delete your entire account.
configs.confirm-for-delete = If you want to delete your account, enter your password to confirm.
configs.cancel = Cancel
configs.delete-my-account = Delete my account
configs.change-password = Change password
configs.current-password = Current Password
configs.new-password = New Password
configs.confirmation = Confirm new password
configs.password-specification-1 = Use a password at least 15 letters long or at least
configs.password-specification-2 = 8 characters long with mixed letters and numbers.

configs.api.description = Main API settings
configs.api.batch = Batch processing
configs.api.cache = Cache management
configs.api.limits = Data limits
configs.api.others = Other API settings

configs.overwritten.user = My account settings override this value
configs.overwritten.app = Application settings override this value

configs.logs.default-level = Default Log Level for the rest
configs.logs.modules = Logging for separate features

configs.logs.debug = Debug Level
configs.logs.info = Info Level
configs.logs.warn = Warning Level
configs.logs.error = Error Level
configs.logs.default = Default Level
configs.api-send_test_email = Send a test email to me

configs.delete_avatar_failed = Deleting avatar failed

configs.help.api-send_test_email = This will send an email to your email address stored in Countly.
configs.help.api-send_test_email_delivered = Test email successfully sent!
configs.help.api-send_test_email_failed = Sending test e-mail failed!
configs.help.api-send_test_email_subject = Countly test email
configs.help.api-send_test_email_message = Hi,<br/><br/>This is Countly test e-mail and it has been successfully delivered.<br/><br/><br/>Enjoy,<br/>A fellow Countly Admin

configs.help.frontend-offline_mode = When enabled, Countly doesnât connect to Intercom to enable in app chat, Google services to enable Google maps and Countly services to track anonymized service usage.
configs.help.frontend-countly_tracking = When enabled, Countly will be activated on this server to perform server-level analytics and gather user feedback to aid us in continuous product improvement. Personal user data/details or the data you process using this server will never be collected or analyzed. All data is sent exclusively to our dedicated Countly server located in Europe.
configs.help.frontend-production = Initial load of dashboard should be faster, due to smaller files and smaller file amount, but when developing a plugin, you need to regenerate them to see changes
configs.help.frontend-theme = Selected theme will be available server-wide, for all apps and users
configs.help.frontend-session_timeout = User will be forced to logout after session timeout (in minutes) of inactivity. If you want to disable force logout, set to 0.
configs.help.frontend-google_maps_api_key = Google requires an API key for Geocharts visualization used in views such as Overview and Analytics > Countries. Provide your API key to use this visualization without any limitations.<br/><a href="https://developers.google.com/maps/documentation/javascript/get-api-key">Learn how to get your API key.</a>
configs.help.security-login_tries = Account will be blocked for some time after provided number of incorrect login attempts. See below for time increments.
configs.help.security-login_wait = Incremental period of time account is blocked after provided number of incorrect login attempts (in seconds)
configs.help.security-password_rotation = Amount of previous passwords user should not be able to reuse
configs.help.security-password_autocomplete = Enable or disable autocomplete on prelogin forms
configs.help.security-robotstxt = Customize to tell search robots what is indexable and what is not
configs.help.security-proxy_hostname = Add your proxy hostname
configs.help.security-proxy_port = Add your proxy port number
configs.help.security-proxy_username = Add your proxy username
configs.help.security-proxy_password = Add your proxy password
configs.help.security-proxy_type = Choose your proxy type
configs.help.api-offline_mode = When enabled, API connections are disabled for email report news, checking new SDKs, pinging external IPs, checking new blogs and making Intercom connections.
configs.help.api-domain = This is the full qualified domain name used in outgoing emails. It should be in the form of http://SERVERNAME or https://SERVERNAME
configs.help.api-safe = If enabled, server will verify key parameters and respond with error or success. This increases server overhead, so use with care.
configs.help.api-session_duration_limit = Maximum session duration value in seconds allowed in any request. If a request contains a session duration higher than this value, it will be capped.
configs.help.api-city_data = Enable tracking city level data in dashboard. If disabled, city information will no longer be added or updated for users.
configs.help.api-country_data = Enable tracking country level data in dashboard. If disabled, country information will no longer be added or updated for users.
configs.help.api-trim_trailing_ending_spaces = If enabled, all trailing and ending spaces will be trimmed from all incoming data.
configs.help.api-event_limit = Maximum number of event keys stored in database. Increasing this number may seriously affect your server performance.
configs.help.api-array_list_limit = If an event segment or custom user property value is an array and its length exceeds the maximum array length set in this setting, only the first n values (where n is the maximum array length) will be retained and any additional values will be discarded.
configs.help.api-event_segmentation_limit = Maximum number of segmentations per custom events. Increasing this number may affect your server performance.
configs.help.api-event_segmentation_value_limit = Maximum number of unique values in each segmentation. Increasing this number may affect your server performance.
configs.help.api-sync_plugins = Toggling plugin will propagate state to all servers with same database. Enabling would propagate plugin state to all server, but also have more overhead performing plugin state checks.
configs.help.api-session_cooldown = Time between session end and start when server will extend previous session instead of new
configs.help.api-total_users = If enabled, total users API will be enabled and used to override estimated total user counts in all reports. Enabling this will provide extra overhead, so consult Countly before enabling for highly loaded servers.
configs.help.api-data_retention_period = Specifies how long to keep granular data before deleting it (0 means never delete it). This setting will only delete granular level data and will not affect aggregated data you see in sections such as Overview and Analytics.
configs.help.api-metric_limit = Number of different metric values per annual period. Increasing this number may affect your server performance.
configs.help.api-metric_changes = Recording changes is required by Total users correction. Disable it if you know you won't be using Total users correction
configs.help.api-request_threshold = Time before switching to report manager if a drill, funnel or other similar dashboard functionality request takes too long to complete. This should not be longer than HTTP timeout, which by default is 60 seconds or you won't get any response.
configs.help.api-export_limit = Amount of lines in a CSV, PDF or CSV document exported in single file
configs.help.api-prevent_duplicate_requests = Stores and compares request hash to prevent duplicate requests
configs.help.api-batch_period = How often to commit batch writes to database (in seconds)
configs.help.api-batch_processing = Combine aggregated data writes together and commit to database with specific frequency
configs.help.api-batch_on_master = If enabled, all processes will send data to single process to commit to database. Helps if you have many CPU cores per instance
configs.help.api-batch_read_processing = Some reads that happen on each SDK call can be cached for reusing
configs.help.api-batch_read_on_master = This will share all cached reads on single proces instead of each process having their own cache. Helps if you have many CPU cores per instance
configs.help.api-batch_read_ttl = How long should cache be kept if unused (in seconds)
configs.help.api-batch_read_period = How often should cache be updated (in seconds)
configs.help.apps-country = Default country to be used when creating app with API and without a country
configs.help.apps-category = Default category to be used, when creating app with API and without a category
configs.help.logs-debug = Log fine-grained informational events that are most useful to debug an application
configs.help.logs-info = Log informational messages that highlight the progress of the application
configs.help.logs-warn = Log potentially harmful situations
configs.help.logs-error = Log error events that might still allow the application to continue running
configs.help.logs-default = Level of debug info of each module by default to output into log file under /log directory
configs.help.crashes-report_limit = Number of reports to display in each crash group's page
configs.help.frontend-use_google = Disable this option, if you are in the country where Google services are blocked. If disabled, certain Google services (e.g maps) wonât be visible.
configs.help.frontend-code = Display links for Countly Code generator under new app creation page.
configs.help.security-dashboard_additional_headers = Add headers for Countly to use on Dashboard responses by default (one header per new line)
configs.help.security-api_additional_headers = Add headers for Countly to use on API responses by default (one header per new line)
configs.help.security-password_min = Minimum number of characters used in the password
configs.help.security-password_char = If enabled, provided passwords must contain at least one uppercase character.
configs.help.security-password_number = If enabled, provided passwords must contain at least one digit (e.g 0..9)
configs.help.security-password_symbol = If enabled, provided passwords must contain at least one special symbol (not a number or latin character)
configs.help.security-password_expiration = Number of days after which user must reset password
configs.help.user-level-configuration = Allow separate dashboard users to change these configs for their account only.
configs.help.security-dashboard_rate_limit_window = Will start blocking if request amount is reached in this time window
configs.help.security-dashboard_rate_limit_requests = How many requests to allow per time window?
configs.help.push-proxyhost = Hostname or IP address of HTTP CONNECT proxy server to use for communication with APN & FCM when sending push notifications.
configs.help.push-proxyport = Port number of the proxy server
configs.help.push-proxyuser = (if needed) Username for proxy server HTTP Basic authentication
configs.help.push-proxypass = (if needed) Password for proxy server HTTP Basic authentication
configs.help.push-proxyunauthorized = (if needed) Allow self signed certificates without CA installed

systemlogs.action.change_configs = Setting Changed
systemlogs.action.change_plugins = Plugins Changed
