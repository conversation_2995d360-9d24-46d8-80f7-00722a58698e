data-manager.plugin-title = Data Manager
data-manager-redaction.title = Data Manager: Redaction
data-manager-transformations.title = Data Manager: Transformations
data-manager.plugin-description = Transform incoming data and configure data types
data-manager.manage-events = Manage Events
data-manager.manage-event-segmentation = Manage Event Segmentation
data-manager.manage-validations = Manage Validations
data-manager.status = Status
data-manager.event-or-property = Related Event/Custom Property
data-manager.transformation-type = Transformation Type
data-manager.transformation = Transformation
data-manager.data-type = Data Type
data-manager.property-name = Property Name
data-manager.property-type = Property Type
data-manager.save-changes = Save Changes
data-manager.discard-changes = Discard Changes
data-manager.new-transformation = + New Transformation
data-manager.transformations= Transformations
data-manager.data-types = Data Types
data-manager.all-transformations = All transformations
data-manager.event = Event
data-manager.events = Events
data-manager.segmentation = Segmentation
data-manager.event-groups = Event Groups
data-manager.validations = Validations
data-manager.user-properties = User Properties
data-manager.custom-property = Custom Property
data-manager.user-property = User Property
data-manager.all-data-sources = All data sources
data-manager.created = Transformation Created!
data-manager.saved = Transformation Saved!
data-manager.make-change-remind = You made 1 change.
data-manager.make-changes-remind = You made {0} changes.
data-manager.status-changed = Status changed
data-manager.delete-transformation = Yes, delete transformation
data-manager.empty-placeholder = -
data-manager.delete-transformation-title = Delete transformation?
data-manager.delete-data-type = Yes, delete property's type
data-manager.delete-data-type-title = Delete property's type?
data-manager.confirm-delete-data-type = Are you sure you want to delete property's type?<br/><br/>It will not delete data itself, but will remove property from segmentation inputs.<br/><br/>But if SDK sends data again, it will appear here again

data-manager.validation.error.data_type_mismatch = Data type planned on UI and sent via SDK don't match together.
data-manager.validation.error.unexpected-segment = Segment sent by SDK is not Live/Approved on the UI
data-manager.validation.error.unexpected-event = Event sent by SDK is not Live/Approved on the UI
data-manager.validation.error.unplanned-segment = Segment for event sent by SDK is not planned on UI
data-manager.validation.error.unplanned-event = Event sent by SDK is not planned on UI
data-manager.validation.error.missing_segmentation = Required Segmentation planned on UI not sent via SDK.
data-manager.validation.error.GLOBAL_REGEX = PII data matched by Global Regex found in data sent by SDK.
data-manager.validation.error.SEGMENT_REGEX = PII data matched by Segment level Regex found in data sent by SDK.

data-manager.name = Name
data-manager.data-type-in-use = Data type in use
data-manager.default-data-type = Default data type
data-manager.default = (Default)
data-manager.related-event = Data Source
data-manager.create-transformation = Create new transformation
data-manager.edit-transformation = Edit transformation
data-manager.what-to-transform = What do you want to transform?
data-manager.how-to-transform = How do you want to transform it?
data-manager.rename-segment = Rename Event segment
data-manager.merge-segment = Merge Event segment
data-manager.rename-property = Rename Custom Property
data-manager.merge-property = Merge Custom Property
data-manager.rename-segment-default = Transformation: Rename event segment
data-manager.select = Select
data-manager.select-event =Select event
data-manager.select-event-segments = Select event segments
data-manager.select-segment-name = New event segment name
data-manager.select-property = Select property
data-manager.select-properties = Select properties
data-manager.select-property-name = Select property name
data-manager.create-transform-button = Create transformation
data-manager.save-transform-button = Save transformation
data-manager.data-types-saved = Data Type changes saved
data-manager.event-label-desc = Select an event, which segmentation you want to edit
data-manager.cp-label-desc = Select custom property to transform
data-manager.new-segment = New segment name
data-manager.type.d = Date
data-manager.type.n = Number
data-manager.type.bl = Big List
data-manager.type.s = String
data-manager.type.l = List
data-manager.type.a = Array
data-manager.transformations-warning = Transformations only affect the incoming, new data and are not retroactive.

data-manager.transform = Transform
data-manager.change-category = Change Category
data-manager.change-status = Change Status
data-manager.change-visibility = Change Visibility
data-manager.create-event = Create new Event
data-manager.regenerate = Regenerate Data
data-manager.event-group = Event Group
data-manager.create-group = Create Event Group
data-manager.create-event-transformation = Create new Event transformation
data-manager.create-segment-transformation = Create new Segment transformation

systemlogs.action.dm-dt-custom = Custom data type changed
systemlogs.action.dm-dt-up = User properties data type changed
systemlogs.action.dm-dt-event-sg = Event segment data type changed
systemlogs.action.dm-transformation = Transformation added
systemlogs.action.dm-toggle-status = Transformation status toggled
systemlogs.action.dm-transformation-delete = Transformation deleted
systemlogs.action.dm-dt-custom-delete = Custom data type deleted
systemlogs.action.dm-dt-up-delete = User property data type deleted
systemlogs.action.dm-dt-event-sg-delete = Event segment data type delete
systemlogs.action.dm-transformation-edit = Transformation edited
systemlogs.action.dm-dt-mask = Property redacted


datamanager.group-event.drawer.name.placeholder = Name your Event
data-manager.import-schema = Import Event Schema
data-manager.export-schema = Export Event Schema

configs.data-manager.manage-events = Manage Events
configs.data-manager.validation-settings = Validation Settings

data-manager.enableValidation = Perform Schema Validations for Incoming Data
configs.help.data-manager-enableValidation = For disabled state, you donât get any error for events, segments or data type validations. <br/>Only global PII regex works for disabled state.

data-manager.allowUnplannedEvents = Manage Events
configs.help.data-manager-allowUnplannedEvents = Allow unplanned events :  Unplanned events are visible on all features that use event data on Countly and tagged as âUnplannedâ on Data Manager. <br/><br/>Don't allow unplanned events :  Unplanned events are NOT visible on all features that use event data on Countly and tagged as âUnplannedâ on Data Manager. This option is recommended to keep your data clean.

data-manager.triggerUnplannedError = Trigger Validation For Unplanned Events
configs.help.data-manager-triggerUnplannedError = You dont get any validation error from Data Manager for disabled state.<br/> Enabled state is highly recommended if Manage Events setting is selected as âDon't allow unplanned eventâ above.

data-manager.segmentLevelValidationAction = Segment Level Validation Action
configs.help.data-manager-segmentLevelValidationAction = Action to take when a segment level regex matches.

data-manager.globalValidationAction = Global Validation Action
configs.help.data-manager-globalValidationAction = Action to take when a global regex matches.
data-manager.globalRegex = Global Validation Regex
configs.help.data-manager-globalRegex = Global Regex to match against data sent by the sdk.
data-manager.redactedLabel = Label for data dropped by Global Validation Regex
configs.help.data-manager-redactedLabel = Masking value to replace the data matched by Global Regex.

data-manager.global = Global
data-manager.global-description = Can be seen by everyone.
data-manager.private = Private
data-manager.private-description = Can be seen by the creator.
data-manager.uncategorized = Uncategorized
data-manager.created = Transformation Created!
data-manager.approved = Approved
data-manager.live = Live
data-manager.blocked = Blocked
data-manager.unplanned = Unplanned
data-manager.edit-event = Edit Event
data-manager.create-new-event = Create New Event
data-manager.edit-event-group = Edit Event Group
data-manager.create-new-event-group = Create New Event Group

data-manager.event-name = Event Name
data-manager.last-modified = Last Modified
data-manager.last-triggered = Last Triggered


data-manager.segmentation.details = Segmentation Details
data-manager.segmentation.delete = Delete Segmentation
data-manager.segmentation.required = Required segmentation
data-manager.segmentation.add = + Add Event Segmentation
data-manager.segmentation.type = Segmentation Type
data-manager.segmentation.select.type = Select Segmentation Type
data-manager.event.name = Enter Name
data-manager.segmentation.key = Segmentation Key
data-manager.segmentation.enter.key = Enter Segmentation Key
data-manager.segmentation.description = Segmentation Description
data-manager.segmentation.enter.description = Enter Segmentation Description
data-manager.events.event-details = Event Details
data-manager.events.event-properties = Event Properties
data-manager.events.event-description = Event Description
data-manager.events.enter-event-description = Enter your description
data-manager.events.key = Event Key
data-manager.events.enter-key = Enter Event Key
data-manager.events.key.tooltip = Event Key
data-manager.category = Category
data-manager.category.tooltip = Category
data-manager.search-category = Search Category
data-manager.visible = Visible
data-manager.hidden = Hidden
data-manager.visibility = Visibility
data-manager.display-name-for-count = Display Name for Count
data-manager.display-name-for-count-subheading = A display name for the count property of this event
data-manager.display-name-for-sum = Display Name for Sum
data-manager.display-name-for-sum-subheading = A display name for the sum property of this event
data-manager.display-name-for-duration = Display Name for Duration
data-manager.display-name-for-duration-subheading = A display name for the duration property of this event
data-manager.count = Count
data-manager.sum = Sum
data-manager.duration = Duration
data-manager.omit-segments = Omit Segments
data-manager.omit-segments.tooltip = It allows to delete segmentations from aggregated data only, and keep recording to granular data as string. Omitted segments will not be saved in the future and past data for these segments will be purged immediately after you save these settings. Data for omitted segments will still be stored in Drill.
data-manager.event-segmentation = Event Segmentation
data-manager.transformation-type = Transformation Type
data-manager.transformation-type.tooltip = Transformation Type
data-manager.transformation-permanent-warning = Transformation is permanent, so modifying all the data cannot be reversed.
data-manager.select-events = Select Events
data-manager.select-events.tooltip = Select Events
data-manager.select-view = Select View
data-manager.regular-expression = Regular Expression
data-manager.regular-expression.tooltip = Regular Expression
data-manager.data-type = Data Type
data-manager.data-type.tooltip = Data Type
data-manager.is-event-new = Is event Pre existing or new
data-manager.existing-event = Existing Event
data-manager.new-event = New Event
data-manager.new-event-name = New Event Name
data-manager.transformation-method = Transformation Method
data-manager.transformation-method.tooltip = Select transformation method that will be applied to data
data-manager.transformation-method.subheading = Select transformation method that will be applied to data
data-manager.incoming = Incoming
data-manager.existing = Existing
data-manager.both = Both
data-manager.rename-type = Rename Type
data-manager.rename-type.tooltip = Rename Type
data-manager.rename-with-value = Rename with Specific Value
data-manager.rename-with-regex = Rename with Regex
data-manager.select-segmentation = Select Segmentation
data-manager.select-segmentation.tooltip = Select Segmentation
data-manager.regular-expression-change-value.tooltip = The regular expression to find segmentation values for alteration. Only string-type segmentations can be modified through regular expressions.
data-manager.value-to-replace = Value to Replace
data-manager.value-to-replace.tooltip = Value to replace the regex match with
data-manager.enter-new-value = Enter New Value
data-manager.new-segmentation-name = New Segmentation Name
data-manager.segmentation-name = Segmentation Name
data-manager.segmentation-details = Segmentation Details
data-manager.new-segmentation-name.placeholder = Name
data-manager.select-property = Select property
data-manager.select-property.tooltip = Select Property
data-manager.select-property.placeholder = Select Property
data-manager.new-property-name = New Property Name
data-manager.last-modified-on = Last modified on
data-manager.description = Description
data-manager.first-triggered = First Triggered
data-manager.segment-name = Segment Name
data-manager.last-modified = Last Modified
data-manager.delete-event-permanently = Delete event(s) permanently?
data-manager.delete-event-warning = Warning: This is not reversible
data-manager.delete-events = Delete Events
data-manager.event-group-details = Event Group Details
data-manager.included-events = Included Events
data-manager.delete-event-group-permanently = Delete Event Group permanently?
data-manager.event-group-name = Event Group Name
data-manager.name-event-group-placeholder = Name your event group
data-manager.event-group-description = Event Group Description
data-manager.event-group-visibility = Event Group Visibility
data-manager.event-group-visibility-subheading = If an event group is invisible server will continue to record data for it but it will not be displayed in the user interface.
data-manager.event-group-is-visible = Event Group is visible
data-manager.event-group-properties = Event Group Properties
data-manager.optional = Optional
data-manager.delete-event-group-permanently = Delete Event Group permanently?
data-manager.delete-event-group = Delete Event Group
data-manager.event-search-placeholder = Search in Events
data-manager.manage-categories = Manage Categories
data-manager.last-modified-by = Last modified by
data-manager.delete-segments-permanently = Delete Segment(s) permanently?
data-manager.omit-segments = Omit Segments
data-manager.omit-segments-subheading = Choose which segments of this custom event to omit. Omitted segments will not be saved in the future and past data for these segments will be purged immediately after you save these settings.
data-manager.omit = Omit
data-manager.related-event = Data Source
data-manager.related-property = Related Property
data-manager.delete-transformation-permanently = Delete Transformation permanently?
data-manager.delete-transformation = Yes, delete transformation
data-manager.error = Error
data-manager.error-last-triggered = Error Last Triggered
data-manager.occurence-count = Occurences
data-manager.approve = Approve
data-manager.delete-validation = Delete validations?
data-manager.approve-validation = Approve validation?
data-manager.missing-segmentation = Missing Segmentation
data-manager.datatype-mismatch = Data Type Mismatch
data-manager.global-validation = Global Validation
data-manager.segment-validation = Segment Validation
data-manager.drag-drop-file = Drag and drop file here or
data-manager.click-to-upload = Click to upload
data-manager.add-category = Add new category
data-manager.category-name = Category name
data-manager.add-category = Add new category
data-manager.back-to-manage-properties = Back to Manage Properties
data-manager.property-details = Property Details
data-manager.edit-property = Edit Property
data-manager.delete-property-permanent =  Delete property permanently?
data-manager.select-data-type = Select Data Type
data-manager.error-first-triggered = Error First Triggered on
data-manager.parent-event = Parent Event
data-manager.request-details = Request Details
data-manager.regeneration-type = Regeneration Type
data-manager.regeneration-type.tooltip = Regeneration Type
data-manager.regeneration-period = Regeneration Period
data-manager.regeneration-period.tooltip = Regeneration Period
data-manager.meta-data = Meta Data
data-manager.aggregated-data = Aggregated Data
data-manager.back-to-manage-segmentation = Back to Manage Segmentation
data-manager.edit-segmentation = Edit Segmentation
data-manager.delete-segment-permanent = Delete Segment permanently?
data-manager.create-new-cta = Create new


data-manager.success.loadTransformations = Transformation Created
data-manager.error.loadTransformations = Error while saving Transform
data-manager.success.updateTransformations = Transformation Updated
data-manager.error.updateTransformations = Error while updating transformation
data-manager.success.regeneration = Regeneration Started
data-manager.error.regeneration = Error in regeneration
data-manager.success.status = Status Updated
data-manager.error.status = Error while updating event status
data-manager.success.segment-update = Segment Updated
data-manager.error.segment-update = Error while updating segment
data-manager.success.segment-delete = Segment Deleted
data-manager.error.segment-delete = Error while deleting segment
data-manager.success.transformation-delete  = Transformation Deleted
data-manager.error.transformation-delete = Error while deleting transformation
data-manager.success.validation-approve = Approved
data-manager.error.validation-approve = Error while approving validation
data-manager.success.validation-delete = Deleted
data-manager.error.validation-delete = Error while deleting validation
data-manager.success.import = Import Completed
data-manager.error.import = Error during import process
data-manager.error.export = Error during import process
data-manager.success.data-type = Data Type Updated
data-manager.error.data-type = Error while updating data type
data-manager.success.data-type-delete = Data Type Deleted
data-manager.error.data-type-delete = Error while deleting data type
data-manager.success.event-group-delete = Event Group Deleted
data-manager.error.event-group-delete = Error while deleting event group
data-manager.success.event-group-create = Event Group Created
data-manager.error.event-group-create = Error while creating event group
data-manager.success.event-group-update = Event Group Updated
data-manager.error.event-group-update = Error while updating event group
data-manager.success.event-create = Event Created
data-manager.error.event-create = Error while creating event
data-manager.success.event-update = Event Updated
data-manager.error.event-update = Error while updating event
data-manager.success.event-delete = Event Deleted
data-manager.error.event-delete = Error while deleting event
data-manager.success.category-create = Category Created
data-manager.error.category-create = Error while creating category
data-manager.success.category-update = Category Updated
data-manager.error.category-update = Error while updating category
data-manager.success.category-delete = Category Deleted
data-manager.error.category-delete = Error while deleting category
data-manager.success.category-change = Category Changed
data-manager.error.category-change = Error while changing category
data-manager.prevented-type-change = Changing type is not allowed for this property.

data-manager.data-masking.title = Data Redaction
data-manager.data-masking.text = If the data is redacted, all queries will be processed to remove it from the response
data-manager.data-masking-property-is-masked = Property is redacted
data-manager.data-masking.masked = Redacted
data-manager.data-masking.unmasked = -
data-manager.data-masking.success-masked = Data redaction changes saved successfully.
data-manager.data-masking.error-masked = Data redaction failed.
data-manager.enableDataMasking = Enable data masking
configs.help.data-manager-enableDataMasking = Once enabled data manager will mask all data from database responses based on settings set in data manager plugin. Data redaction will not happen in plugins: complaince hub and audit logs.

data-manager.transform-save = Save Transform
data-manager.transform.event-option.merge = Merge by selecting events
data-manager.transform.event-option.merge.description = Merge by selecting events
data-manager.transform.event-option.merge-regex = Merge by Regex
data-manager.transform.event-option.merge-regex.description = Merge by Regex

data-manager.transform.segment-option.merge = Merge Segmentation
data-manager.transform.segment-option.merge.description = Merge Segmentation
data-manager.transform.segment-option.change-value = Modify Segmentation Value
data-manager.transform.segment-option.change-value.description = Modify Segmentation Value
data-manager.transform.segment-option.rename = Rename Segmentation
data-manager.transform.segment-option.rename.description = Rename Segmentation

data-manager.transform.property-option.merge = Merge Custom Property
data-manager.transform.property-option.merge.description = Merge Custom Property
data-manager.transform.property-option.change-value = Modify Custom Property Value
data-manager.transform.property-option.change-value.description = Modify Custom Property Value
data-manager.transform.property-option.rename = Rename Custom Property
data-manager.transform.property-option.rename.description = Rename Custom Property
data-manager.error.event-visibility-error = Cannot Change visibility for unplanned events.
data-manager.property-expiration = Expiration
data-manager.property.session-end = Session End
data-manager.error.property-expire = Error in setting Property Expiration
