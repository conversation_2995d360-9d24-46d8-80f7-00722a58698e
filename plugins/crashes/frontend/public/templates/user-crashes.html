<div v-bind:class="[componentId]">
  <div class="text-big font-weight-bold bu-my-4 bu-pt-4">
    {{i18n('crashes.unresolved-crashes')}}
  </div>
  <cly-datatable-n :force-loading="isLoading" :rows="userCrashesData" :exportFormat="formatExportFunction">
    <template v-slot="scope">
      <el-table-column prop="name" column-key="group" :label="i18n('crashes.error')" min-width="360" sortable>
        <template v-slot="rowScope">
          <div style="color: #32659D; text-decoration: none;" class="crash-link">
            <a :href="rowScope.row.link">{{rowScope.row.group}}</a>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="reports" :label="i18n('crashes.reports')" min-width="180" sortable>
        <template v-slot="rowScope">
          <div>
            {{rowScope.row.reports}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="time" column-key="last" :label="i18n('crashes.last_time')" min-width="210" sortable>
        <template v-slot="rowScope">
          <div>
            {{getDateAndTime(rowScope.row.last)}}
          </div>
        </template>
      </el-table-column>
    </template>
  </cly-datatable-n>
</div>