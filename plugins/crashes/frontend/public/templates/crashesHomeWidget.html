<div v-bind:class="[componentId]">
	<clyd-home-widget-header :widget="headerData"></clyd-home-widget-header>
	<cly-section>
		<cly-metric-cards :multiline="false" v-loading="isLoading" style="min-height:100px;">
				<cly-metric-card :label="item.name" :test-id="'crashes-index-' + idx" :tooltip="item.info" :isEstimate="item.isEstimate" :estimateTooltip="i18n('common.estimation')" :box-type="5" formatting="long" color="#097EFF" numberClasses="bu-is-flex bu-is-align-items-center" :key="idx" v-for="(item, idx) in crashesItems">
					<!-- {{item.name}}
					<cly-tooltip-icon v-if="item.info" class="bu-ml-2" icon="ion-help-circled" :tooltip="item.info"></cly-tooltip-icon> -->
					<template v-slot:number>{{item.value}}</template>
					<span v-if="item.trend=='d'" slot="description" :class="['cly-trend-down', item.reverse ? 'negated' : '']" :data-test-id="'metric-card-crashes-index-' + idx + '-coloumn-trend-desc'"><i class="cly-trend-down-icon ion-android-arrow-down" :data-test-id="'metric-card-crashes-index-' + idx + '-coloumn-trend-icon'"></i>{{item.change}}</span>
					<span v-else-if="item.trend=='u'" slot="description" :class="['cly-trend-up', item.reverse ? 'negated' : '']" :data-test-id="'metric-card-crashes-index-' + idx + '-coloumn-trend-desc'"><i class="cly-trend-up-icon ion-android-arrow-up" :data-test-id="'metric-card-crashes-index-' + idx + '-coloumn-trend-icon'"></i>{{item.change}}</span>
					<span v-else slot="description" class="cly-trend-neutral" :data-test-id="'metric-card-crashes-index-' + idx + '-coloumn-trend-desc'"><i class="cly-trend-up-icon cly-trend-neutral-icon ion-minus-round" :data-test-id="'metric-card-crashes-index-' + idx + '-coloumn-trend-icon'"></i>{{item.change}}</span>
				</cly-metric-card>
		</cly-metric-cards>
	</cly-section>
</div>