<div>
    <clyd-appcount v-model="scope.editedObject.app_count"></clyd-appcount>
    <clyd-sourceapps v-model="scope.editedObject.apps" :multiple="scope.editedObject.app_count === 'multiple'"></clyd-sourceapps>
    <clyd-visualization v-model="scope.editedObject.visualization" :enabled-types="enabledVisualizationTypes"></clyd-visualization>
    <clyd-metric v-model="scope.editedObject.metrics" :metrics="metrics" :multiple="isMultipleMetric"></clyd-metric>
    <cly-form-field-group :label="i18n('dashboards.additional-settings')">
        <clyd-title v-model="scope.editedObject.title"></clyd-title>
    </cly-form-field-group>
</div>