<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Click Example</title>
    <script type='text/javascript' src='https://cdn.jsdelivr.net/npm/countly-sdk-web@24.11.2/lib/countly.js'></script>
    <script type='text/javascript'>

        //initializing countly with params
        const COUNTLY_SERVER_KEY = "https://onur-journey.count.ly/";
        const COUNTLY_APP_KEY = "5e70a9c12794a088f63b0dc41a8a17c66ed5fd02";

        if (COUNTLY_APP_KEY === "YOUR_APP_KEY" || COUNTLY_SERVER_KEY === "https://your.server.ly") {
            console.warn("Please do not use default set of app key and server url")
        }
        // initializing countly with params
        Countly.init({
            app_key: COUNTLY_APP_KEY,
            url: COUNTLY_SERVER_KEY, //your server goes here
            debug: true
        })
        //track sessions automatically
        Countly.track_sessions();
        //track pageviews automatically
        Countly.track_pageview();
        Countly.track_errors();
        Countly.content.enterContentZone();
    </script>
    <script>
        function handleButton1Click() {
          Countly.q.push(['add_event',{
            "key": "changePlan",
            "count": 1,
            "segmentation": {
              "New Plan": "Family"
            }
          }]);

          Countly.q.push(['user_details',{
            "custom":{
              "Plan": "Family"
            }
          }]);

          Countly.q.push(['userData.save']);
        }

        function handleButton2Click() {
          Countly.q.push(['add_event',{
            "key": "changePlan",
            "count": 1,
            "segmentation": {
              "New Plan": "Premium"
            }
          }]);

          Countly.q.push(['user_details',{
            "custom":{
              "Plan": "Premium"
            }
          }]);

          Countly.q.push(['userData.save']);
        }

        function handleButton3Click() {
          Countly.q.push(['add_event',{
            "key": "watchContent",
            "count": 1,
            "duration": Math.random(60,3000),
            "segmentation": {
              "Content Type": "Movie"
            }
          }]);
        }

        function handleButton4Click() {
          Countly.q.push(['add_event',{
            "key": "shareContent",
            "count": 1,
            "segmentation": {
              "Content Type": "Movie"
            }
          }]);
        }
    </script>
</head>
<body>
    <h1>Journey Example</h1>
    <button id="button1" onclick="handleButton1Click()">Event Buy Family Plan</button>
    <button id="button2" onclick="handleButton2Click()">Event Buy Premium Plan</button>
    <button id="button3" onclick="handleButton3Click()">Event Watch Content</button>
    <button id="button4" onclick="handleButton4Click()">Event Share Content</button>
</body>
</html>
