<html>
<style>
    body {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
    }
</style>
<head>
    <script type='module'>
        const response = {
                "html": "http://stable-je-cb.count.ly/_external/content?id=67447c4144eca5daadadc7fc&uid=2X&app_id=66fa992b8757e0f5c3a52cfb&skipViewRecord=true",
                "geo": {
                    "l": {
                        "x": 282,
                        "y": 56,
                        "w": 303,
                        "h": 400
                    },
                    "p": {
                        "x": 62,
                        "y": 325.5,
                        "w": 288,
                        "h": 216
                    }
                }
            };

            var iframe = document.createElement("iframe");
            iframe.src = response.html;
            iframe.style.position = "absolute";
            iframe.style.left = response.geo.l.x + "px";
            iframe.style.top = response.geo.l.y + "px";
            iframe.style.width = response.geo.l.w + "px";
            iframe.style.height = response.geo.l.h + "px";
            iframe.style.border = "none";
            iframe.style.zIndex = "999999";
            document.body.appendChild(iframe);
    </script>
</head>

<body
    style="background-image: url('https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fpixnio.com%2Ffree-images%2F2017%2F09%2F26%2F2017-09-26-07-22-55.jpg&f=1&nofb=1&ipt=bc01c79fcdabb82efba3ffcd53430ddcd9b04d7816c4a0f8f3065d9ebcecb7b5&ipo=images'); background-size: cover; background-repeat: no-repeat;">
</body>


</html>